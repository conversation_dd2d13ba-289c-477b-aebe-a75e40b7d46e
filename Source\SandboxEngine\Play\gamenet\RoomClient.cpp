//这块代码不能用老的
#include "RakPeerInterface.h"

//#ifdef _WIN32
//#ifdef _DEBUG
//#pragma comment(lib, "RakNet_d.lib")
//#else
//#pragma comment(lib, "RakNet.lib")
//#endif
//#endif

#include "GameNetDefine.h"
//#include "cs_net.h"

#include "MessageIdentifiers.h"
//这块代码不能用老的
#include "RakNetTypes.h"
#include "RakPeerInterface.h"
#include "GetTime.h"
#include "timeutil.h"

#include <string>
#include <cstdio>

#include "RoomClient.h"
#include "RoomClientHandler.h"
#include "GameNetManager.h"
#include "OgreUtils.h"
#include "Platforms/PlatformInterface.h"
//#include "GameEvent.h"
#include <sstream>
#include "OgreStringUtil.h"
#include "Common/OgreTimer.h"
#include "File/FileManager.h"
#include "IClientGameInterface.h"
#include "OgreScriptLuaVM.h"
#include "WorldManager.h"
#include "GlobalFunctions.h"
#include "SandboxGFunc.h"
#include "ClientInfoProxy.h"
#include "DefManagerProxy.h"
#include "IWorldConfigProxy.h"
#include "GameInfoProxy.h"
#include "gettimeofday.h"
#include "Network/HttpManager.h"
#include "Bootstrap/BootConfig.h"
#include "ClientInfoProxy.h"
#include "SandboxCoreDriver.h"
#include "LuaInterfaceProxy.h"
#include "IClientGameManagerInterface.h"
#include "ICloudProxy.h"
#include "BitStream.h"
#include "GameAnalytics.h"
#ifdef IWORLD_SERVER_BUILD
#ifdef _WIN32
#include <Windows.h>
#include <Psapi.h>
#include <Iphlpapi.h>
#pragma comment(lib, "Iphlpapi.lib")
#pragma comment(lib, "Psapi.lib")
#else
#include <sys/sysinfo.h>
#include <unistd.h>
#include <fstream>
#include <iostream>
#include <sstream>
#include <string>
#include <unistd.h>
#include <cmath>
#include <chrono>
#include <thread>
#endif
#endif

#if OGRE_PLATFORM != OGRE_PLATFORM_WIN32
#include <sys/time.h>
#endif
using namespace MINIW;
using namespace MNSandbox;

#ifdef USE_CONSOLE_OUTPUT
#undef LOG_INFO
#define LOG_INFO printf
#endif

const int LAN_UPDATE_INTERVAL = 3000;
const int LAN_SERVER_PORT = 60008;
const int LAN_CLIENT_PORT = 60009;
std::string g_RoomIP;
using namespace Rainbow;
using namespace Rainbow::Http;

// todo 临时宏，新下载功能完成后，应将其删去
// todo 尽量将HandleRequestEnd及其内容改造为static，无需传递this
#define ROOM_USE_NEW_HTTPMANAGER 1
static UInt32 s_TaskID = 0;

const std::string CustomFlagArg = "audit_flag";

//core::hash_map< RoomClient*, dynamic_array<RoomClient::ROOM_TASK_DATA*> > s_RoomTaskArray;

struct RoomReqBuilder
{
	std::ostringstream url;
	std::ostringstream authparams;
	int paramCount;
	std::string authStr;

	std::ostringstream md5OriStm; //计算请求串的MD5 可以用来判断跟其他请求是否相同（不是鉴权那个MD5）
	std::set<std::string> md5IngorKeys; //不参与md5OriStr计算的key（按各人需求，有些不重要的参数补参与计算 比如 host_update_room里面的ping）
	
	RoomReqBuilder(std::string server, const char* path)
	{
		url << server << path << "?";
		this->paramCount = 0;
		authStr = "";
	}

	//设置不参与计算《参数是否变化》的的参数名，（不是鉴权那个）
	RoomReqBuilder& setIngorTokenMd5Arg(const char* name)
	{
		md5IngorKeys.insert(std::string(name));
		return (*this);
	}
	
	RoomReqBuilder& addParam(const char* name, const char* value, bool url_escape = false)
	{
		if (paramCount > 0)
		{
			url << "&";
			authparams << "&";
		}

		if (url_escape)
			url << name << "=" << gFunc_urlEscape(value);
		else
			url << name << "=" << value;

		authparams << name << "=" << value;

		if (md5IngorKeys.end() == md5IngorKeys.find(std::string(name)))
			md5OriStm << name << "=" << value << "&";
		
		paramCount++;
		return (*this);
	}
	RoomReqBuilder& addParam(const char* name, const std::string& value, bool url_escape = false)
	{
		if (paramCount > 0)
		{
			url << "&";
			authparams << "&";
		}

		if (url_escape)
			url << name << "=" << gFunc_urlEscape(value);
		else
			url << name << "=" << value;

		authparams << name << "=" << value;

		if (md5IngorKeys.end() == md5IngorKeys.find(std::string(name)))
			md5OriStm << name << "=" << value << "&";
		
		paramCount++;
		return (*this);
	}
	RoomReqBuilder& addParam(const char* name, unsigned int value)
	{
		if (paramCount > 0)
		{
			url << "&";
			authparams << "&";
		}

		url << name << "=" << value;
		authparams << name << "=" << value;

		if (md5IngorKeys.end() == md5IngorKeys.find(std::string(name)))
			md5OriStm << name << "=" << value << "&";
		
		paramCount++;
		return (*this);
	}
	RoomReqBuilder& addParam(const char* name, int value)
	{
		if (paramCount > 0)
		{
			url << "&";
			authparams << "&";
		}

		url << name << "=" << value;
		authparams << name << "=" << value;

		if (md5IngorKeys.end() == md5IngorKeys.find(std::string(name)))
			md5OriStm << name << "=" << value << "&";


		paramCount++;
		return (*this);
	}

	RoomReqBuilder& calcAuth()
	{
		std::string paramsStr = authparams.str();
		authStr = gFunc_getmd5(paramsStr + "f5711eb1640712de051e5aedc35329c3");
		return (*this);
	}

	//反馈请求参数的MD5，与上次一次请求的tokenMD5比较是否相同，来判断参数是否有变化
	std::string tokenMD5()
	{
		std::string oriStr = md5OriStm.str();
		return gFunc_getmd5(oriStr);
	}

	std::string end()
	{
		if (authStr.length() == 0)
		{
			calcAuth();
		}
		url << "&auth=" << authStr;

		return url.str();
	}
};

bool RoomClient::s_serverConfigPulled = false;
int RoomClient::s_serverConfigPulledTime = 0;
int RoomClient::s_maxver = 0;
int RoomClient::s_minver = 0;
int RoomClient::s_latestver = 0;
int RoomClient::s_province = 0;
int RoomClient::s_isp = 0;
std::string RoomClient::s_roomZone = "";
std::vector<RoomClient*> RoomClient::m_deleteRoomClients;
//std::string RoomClient::s_nip = "0.0.0.0";
//int RoomClient::s_nport = 0;
//std::string RoomClient::s_pip = "0.0.0.0";
//int RoomClient::s_pport = 0;
int ParseJsonObj(const char* content, jsonxx::Object& parseResult) {
	
	int result = 0;
	if (!parseResult.parse(content))
	{
		result = RoomErr_JsonParseFail;
	}

	if (result == 0)
	{
		if (parseResult.has<jsonxx::Number>("result"))
			result = (int)parseResult.get<jsonxx::Number>("result");
		else
			result = RoomErr_MissingResult;
	}
	return result;
}

//针对现网崩溃增加的有效性检查 code_by:huangfubin 2022.10.24
static unsigned int g_room_client_id = 0;
static std::map<RoomClient*, unsigned int> g_room_client_map;

#ifdef IWORLD_SERVER_BUILD
#ifdef _WIN32
__int64 Filetime2Int64(const FILETIME&  ftime)
{
	LARGE_INTEGER li;
	li.LowPart = ftime.dwLowDateTime;
	li.HighPart = ftime.dwHighDateTime;
	return li.QuadPart;
}

__int64 CompareFileTime2(const FILETIME&  preTime, const FILETIME&  nowTime)
{
	return Filetime2Int64(nowTime) - Filetime2Int64(preTime);
}
#else
// 获取当前进程的PID
int getCurrentProcessId() {
	return getpid();
}

// 获取当前进程的CPU使用时间
long getCurrentCpuTime() {
	int pid = getCurrentProcessId();
	std::ifstream statFile("/proc/" + std::to_string(pid) + "/stat");

	// 读取 stat 文件的内容
	std::string line;
	std::getline(statFile, line);

	std::istringstream iss(line);

	// 跳过前面的字段
	for (int i = 0; i < 13; ++i) {
		std::string field;
		iss >> field;
	}

	// 获取 utime 和 stime 字段的值
	long utime = 0, stime = 0;
	iss >> utime >> stime;

	return utime + stime;
}

// 获取系统的总CPU时间
long getTotalCpuTime() {
	std::ifstream statFile("/proc/stat");

	// 读取第一行的内容
	std::string line;
	std::getline(statFile, line);

	std::istringstream iss(line);

	// 跳过第一个字段（cpu）
	std::string cpu;
	iss >> cpu;

	// 获取各个字段的值
	long user, nice, system, idle, iowait, irq, softirq;
	iss >> user >> nice >> system >> idle >> iowait >> irq >> softirq;

	return user + nice + system + idle + iowait + irq + softirq;
}
#endif // _WIN32


double getCpuUsage() {
	double cpuUsage = 0.0;

#ifdef _WIN32
	// Windows code
	static FILETIME prevIdleTime = { 0, 0 };
	static FILETIME prevKernelTime = { 0, 0 };
	static FILETIME prevUserTime = { 0, 0 };

	FILETIME idleTime, kernelTime, userTime;
	if (GetSystemTimes(&idleTime, &kernelTime, &userTime)) {
		ULONGLONG idleTimeDelta = CompareFileTime2(prevIdleTime, idleTime);
		ULONGLONG kernelTimeDelta = CompareFileTime2( prevKernelTime, kernelTime);
		ULONGLONG userTimeDelta = CompareFileTime2(prevUserTime, userTime);

		if (kernelTimeDelta + userTimeDelta == 0)
			return 0;

		// 保存当前时间作为下一次统计的参考时间
		prevIdleTime = idleTime;
		prevKernelTime = kernelTime;
		prevUserTime = userTime;

		return 100.0 * (kernelTimeDelta + userTimeDelta - idleTimeDelta) / (kernelTimeDelta + userTimeDelta);
	}
#else
	static long prevCpuTime = getCurrentCpuTime();
	static long prevTotalCpuTime = getTotalCpuTime();

	long currCpuTime = getCurrentCpuTime();
	long currTotalCpuTime = getTotalCpuTime();

	// 计算CPU使用率
	cpuUsage = (double)(currCpuTime - prevCpuTime) / (currTotalCpuTime - prevTotalCpuTime) * 100.0;
#ifdef DEDICATED_SERVER
	static int numCores = sysconf(_SC_NPROCESSORS_ONLN);
	cpuUsage *= numCores;   // 转换成单核占比
#endif

	// 更新前一次的CPU时间和总CPU时间
	prevCpuTime = currCpuTime;
	prevTotalCpuTime = currTotalCpuTime;
#endif

	return cpuUsage;
}

unsigned long long getMemoryUsage() {
	unsigned long long memoryUsage = 0;

#ifdef _WIN32
	// Windows code
	MEMORYSTATUSEX memStatus;
	memStatus.dwLength = sizeof(memStatus);
	GlobalMemoryStatusEx(&memStatus);
	memoryUsage = memStatus.dwMemoryLoad;
#else
	// 打开进程的 /proc/self/statm 文件
	std::ifstream statmFile("/proc/self/statm");
	if (!statmFile) {
		std::cerr << "Failed to open /proc/self/statm" << std::endl;
		return 0.0;
	}

	// 读取第一个字段，即程序的虚拟内存大小（以页面为单位）
	long long vmSize;
	statmFile >> vmSize;

	// 获取每个页面的大小（以字节为单位）
	long pageSize = sysconf(_SC_PAGESIZE);

	// 计算虚拟内存大小（以字节为单位）
	long long vmSizeBytes = vmSize * pageSize;

	// 获取当前进程的物理内存占用（以页为单位）
	std::ifstream statusFile("/proc/self/status");
	if (!statusFile) {
		std::cerr << "Failed to open /proc/self/status" << std::endl;
		return 0.0;
	}

	std::string line;
	while (std::getline(statusFile, line)) {
		if (line.compare(0, 6, "VmRSS:") == 0) {
			// 读取物理内存占用字段（以KB为单位）
			long long rss;
			sscanf(line.c_str(), "%*s %lld", &rss);

			// 计算物理内存占用（以字节为单位）
			long long rssBytes = rss * 1024;

			// 计算内存使用率（以百分比为单位）
			memoryUsage = static_cast<double>(rssBytes) / vmSizeBytes * 100;
			return memoryUsage;
		}
	}
#endif

	return memoryUsage;
}

unsigned long long getMaxMemory() {
	unsigned long long maxMemory = 0;

#ifdef _WIN32
	// Windows code
	MEMORYSTATUSEX status;
	status.dwLength = sizeof(status);
	GlobalMemoryStatusEx(&status);
	maxMemory = status.ullTotalPhys;
#else
	// Linux code
	struct sysinfo info;
	sysinfo(&info);
	maxMemory = info.totalram * info.mem_unit;
#endif

	return maxMemory;
}

unsigned long long getProcessMemoryBytes() {
	unsigned long long memoryUsage = 0;
#ifdef __PC_LINUX__ 
	std::ifstream statFile("/proc/self/statm");
	std::string line;
	std::getline(statFile, line);
	statFile.close();

	unsigned long long size, resident, shared, text, lib, data, dt;
	sscanf(line.c_str(), "%llu %llu %llu %llu %llu %llu %llu", &size, &resident, &shared, &text, &lib, &data, &dt);

	static long pageSize = sysconf(_SC_PAGESIZE);

	memoryUsage = resident * pageSize;
#endif

	return memoryUsage;
}

unsigned long long getProcessMemoryLimit() {
	static unsigned long long memLimit = 0;
	static std::string conf;
	if (memLimit > 0)
	{
		return memLimit;
	}
#ifdef __PC_LINUX__
	// ubuntu
	std::string path = std::string("/sys/fs/cgroup/room/") + GetClientInfoProxy()->getEnterParam("port") + "/memory.max";
	std::ifstream statFile(path.c_str());
	if (!statFile)
	{
		// centos
		path = std::string("/sys/fs/cgroup/memory/room/") + GetClientInfoProxy()->getEnterParam("port") + "/memory.limit_in_bytes";
		statFile = std::ifstream(path.c_str());
		if (!statFile)
		{
			memLimit = 1677721600;  // 默认1.6G	
			return memLimit;
		}
	}
	std::string line;
	std::getline(statFile, line);
	statFile.close();
	conf = line;

	sscanf(line.c_str(), "%llu", &memLimit);
 #endif
	return memLimit;
}




// 获取当前进程的接收流量（字节数）
unsigned long long GetProcessRxBytes()
{
	unsigned long long rxBytes = 0;

#ifdef _WIN32

	MIB_TCPSTATS tcpStats;
	if (GetTcpStatistics(reinterpret_cast<PMIB_TCPSTATS>(&tcpStats)) == NO_ERROR)
	{
		rxBytes = tcpStats.dwInSegs * sizeof(MIB_TCPROW_OWNER_MODULE);
	}

#else 
	std::ifstream devFile("/proc/self/net/dev");
	if (!devFile) {
		std::cerr << "Failed to open /proc/self/net/dev" << std::endl;
		return 0;
	}

	std::string line;
	while (std::getline(devFile, line)) {
		if (line.find(":") != std::string::npos) {
			std::istringstream iss(line);
			std::string interface;
			iss >> interface >> rxBytes;

			if (interface == "lo") {
				// 排除回环接口
				continue;
			}

			return rxBytes;
		}
	}
#endif

	return rxBytes;
}

// 获取当前进程的发送流量（字节数）
unsigned long long GetProcessTxBytes()
{
	unsigned long long txBytes = 0;

#ifdef _WIN32
	MIB_TCPSTATS tcpStats;
	if (GetTcpStatistics(reinterpret_cast<PMIB_TCPSTATS>(&tcpStats)) == NO_ERROR)
	{
		txBytes = tcpStats.dwOutSegs * sizeof(MIB_TCPROW_OWNER_MODULE);
	}
#else
	std::ifstream devFile("/proc/self/net/dev");
	if (!devFile) {
		std::cerr << "Failed to open /proc/self/net/dev" << std::endl;
		return 0;
	}

	std::string line;
	while (std::getline(devFile, line)) {
		if (line.find(":") != std::string::npos) {
			std::istringstream iss(line);
			std::string interface;
			iss >> interface >> txBytes;

			if (interface == "lo") {
				// 排除回环接口
				continue;
			}

			return txBytes;
		}
	}
#endif

	return txBytes;
}

double GetProcessRxRate() {
	static unsigned long long prevRxBytes = GetProcessRxBytes();
	unsigned long long currRxBytes = GetProcessRxBytes();
	unsigned long long rxBytesDiff = currRxBytes - prevRxBytes;
	double rxRate = (rxBytesDiff / 1024.0) / 1.0; // 接收速率（KB/s）

	prevRxBytes = currRxBytes;
	return rxRate;
}

double GetProcessTxRate() {
	static unsigned long long prevTxBytes = GetProcessTxBytes();
	unsigned long long currTxBytes = GetProcessTxBytes();
	unsigned long long txBytesDiff = currTxBytes - prevTxBytes;
	double txRate = (txBytesDiff / 1024.0) / 1.0; // 发送速率（KB/s）
	prevTxBytes = currTxBytes;
	return txRate;
}

#endif

NsVersionRaknetInfoReportConfig::NsVersionRaknetInfoReportConfig()
{
	init          = false;
	open          = false;
	open_all      = false;
	interval      = 0;
	max_ping      = 0;
	max_lost_rate = 0;
}

RoomClient::RoomClient()
	: mpClientPeer(nullptr), m_isHost(false), m_lastHostUpdateTime(0)
{
	m_ingameRoomServerCfg = NULL;
	ResetRoom();

#ifdef IWORLD_SERVER_BUILD
	//租赁服未配置roomserverip
	m_hasRoomServerIp = false;
	if (GetGameInfoProxy()->GetRoomHostType() == ROOM_SERVER_RENT)
	{
		const char *t = BootConfig::GetValue("roomserverip");
		if (t && strlen(t) >= 7)   //0.0.0.0
		{
			m_hasRoomServerIp = true;
		}
	}
#endif

	//LOG_INFO("RoomClient  ---222---   %p", this);
#if ROOM_USE_NEW_HTTPMANAGER
#else
	MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().CreateEventDispatcher("GE_HTTP_DOWNLOAD_PROGRESS");
	m_downloadProgressCallback = SandboxEventDispatcherManager::GetGlobalInstance().SubscribeEvent("GE_HTTP_DOWNLOAD_PROGRESS", nullptr, [&](SandboxContext context) -> SandboxResult {
		
		int taskid = (int)context.GetData_Number("task_id");
		int progress = (int)context.GetData_Number("progress");
		//LOG_INFO("RoomClient  ---333---   %p", this);
		downloadProgress(taskid, progress);
		deleteRoomClient();
		return SandboxResult(nullptr, true);

	});

	//针对线上崩溃增加的有效性检查 code_by:huangfubin 2022.10.24
	m_UniqueId = ++g_room_client_id;
	g_room_client_map[this] = m_UniqueId;

#endif

	//针对线上崩溃增加的有效性检查 code_by:huangfubin 2022.10.24
	m_UniqueId = ++g_room_client_id;
	g_room_client_map[this] = m_UniqueId;
	m_nDangerNight = 0;
	m_rentOwnerLeaveTick = 0;
	m_alreadyUsePersonalRentRoomItem = false;
}

void RoomClient::ResetRoom()
{
	// 在地图内的房主 不可以清理数据
	if (m_isHost)
	{
		LOG_INFO("clientroom ishost do not reset!");
		MINIW::ScriptVM::game()->callFunction("OnRoomReset", "s", "host clientroom want reset");
		return;
	}
	m_isHost = false;
	m_isLan = false;

	m_HeartbeatTicks = -1;
	m_HeartbeatTicksRent = 0;
	m_HeartbeatRentStartTime = 0;
	m_LastHeartbeatReturn = -1;
	m_lastRoomSvrPing = -1;
	m_HeartbeatTicksReport = 0;

	m_RoomMembers.clear();
	m_StayRoomMembers.clear();
	m_ProxyOnlyCountrys.clear();
	
	mpListener = nullptr;
	
	mNetCfg = GameNetCfg();
	m_lastPingSendTime = 0;
	m_hasRoomServerIp = false;
	m_getRoomListFromFlag = "";

	m_lasPingTick.tv_sec = 0;
	m_lasPingTick.tv_usec = 0;

	gettimeofday(&m_lasPingTick, NULL);
	memset(&mCurrentRoom, 0, sizeof(mCurrentRoom));

	m_UIN = 0;

	m_lastUploadThumbOwid = 0;

	m_nCurrentRoomOwner = 0;
	m_isLocked = false;
	m_lastStage = 0;
	m_lastRoomJoinable = 1;
	m_lastPingRent = 0;

	m_CurrentSearchUin = 0;
	m_bReCreatRoom = false;
	m_bHasBeLocked = false;

	m_Pause = 0;

	// m_downloadProgressCallback = nullptr; 需要保留，析构时使用
	ENG_DELETE(m_ingameRoomServerCfg);
	disconnect();
}

RoomClient::~RoomClient()
{
	LOG_INFO("RoomClient  ---111---   %p", this);
	SandboxEventDispatcherManager::GetGlobalInstance().Unsubscribe("GE_HTTP_DOWNLOAD_PROGRESS", m_downloadProgressCallback);
	disconnect();
	m_downloadProgressCallback = nullptr;
	ENG_DELETE(m_ingameRoomServerCfg);

	//auto iter = s_RoomTaskArray.find(this);
	//for(int i=0; i< iter->second.size(); ++i)
	//{
	//	ENG_DELETE_LABEL(iter->second[i], kMemGame);
	//}
	//if(s_RoomTaskArray.find(this) != s_RoomTaskArray.end())
	//	s_RoomTaskArray.erase(this);

	//针对线上崩溃增加的有效性检查 code_by:huangfubin 2022.10.24
	g_room_client_map.erase(this);

	for (auto iter : m_httptask_array)
	{
		ROOM_TASK_DATA* data = static_cast<ROOM_TASK_DATA*>(iter.second->GetUserData());
		data->room_client = nullptr;
		//ENG_DELETE_LABEL(data, kMemGame);

		//清除掉此处的回调函数
		//iter.second->ClearCallback();

		iter.second->Break();
	}

	m_httptask_array.clear();

	//针对线上崩溃增加的有效性检查 code_by:huangfubin 2022.10.24
	g_room_client_map.erase(this);
}

//static void DeleteRoomClient(RoomClient*roomClient) {
	//bool isUnsubscribe = SandboxEventDispatcherManager::GetGlobalInstance().Unsubscribe("GE_HTTP_DOWNLOAD_PROGRESS", roomClient.m_downloadProgressCallback);
	//if (isUnsubscribe)
	//{

//		ENG_DELETE(roomClient);
	//}
//}

//bool RoomClient::findTaskData(RoomClient* room_client)
//{
//	return s_RoomTaskArray.find(room_client) != s_RoomTaskArray.end();
//}

int RoomClient::getRoomMemberCount() {
	return mCurrentRoom.PlayerCount;
}

bool RoomClient::connectLobby(std::string genid)
{
	m_isLan = false;

	m_UIN = GetClientInfoProxy()->getUin();
	m_HeartbeatTicks = -1;
	m_LastHeartbeatReturn = -1;
	connectNearbyLan();
	return reqLoginRoomServer(genid);
}

bool RoomClient::connectLan(std::string genid)
{
	assert(mpClientPeer == NULL);
	mpClientPeer = RakNet::RakPeerInterface::GetInstance();
//	mpClientPeer->SetTimeoutTime(GAMENET_TIMEOUT, RakNet::UNASSIGNED_SYSTEM_ADDRESS);
	m_isLan = true;

	//RakNet::SocketDescriptor tmpSockDesc(LAN_CLIENT_PORT, 0);
	RakNet::SocketDescriptor tmpSockDesc(0, 0);
	RakNet::StartupResult startupResult = mpClientPeer->Startup(1, &tmpSockDesc, 1);

	if(RakNet::RAKNET_STARTED != startupResult && RakNet::RAKNET_ALREADY_STARTED != startupResult)
	{
		//TODO log failed
		//ROOM_DEBUG("Net startupResult error.")
		char szErrDetails[128];
		snprintf(szErrDetails, sizeof(szErrDetails), "Room Process Error At:(%d) - %s", __LINE__, "Net startupResult error.");
		MINIW::ScriptVM::game()->callFunction("RoomProcessErrorLog", "s", szErrDetails);
		LOG_INFO("Connect roomserver init failed");
		return false;
	}

	if(mpListener)
	{
		PB_LoginRoomServerRes loginRoomServerRes;
		loginRoomServerRes.set_resultcode(PB_ROOM_OP_SUCCESS);
		loginRoomServerRes.set_minversion(0);
		loginRoomServerRes.set_maxversion(0xffffff);
		loginRoomServerRes.set_latestversion(0xffffff);

		mpListener->onRSLoginRes(loginRoomServerRes, genid);
	}
	
	return true;
}

bool RoomClient::isServerRoom(int uin)
{
	for (int i = 0; i < (int)m_searchUinResults.size(); i++)
	{
		RoomDesc rm = m_searchUinResults[i].second;
		if(rm.owneruin == uin)
		{
			return rm.isServer;
		}
	}

	int roomNum = 0;
	MNSandbox::GetGlobalEvent().Emit<int&>("RoomManager_getNumRoom", roomNum);
	for(int i = 0; i<roomNum; i++)
	{
		RoomDesc * pRm = nullptr;
		MNSandbox::GetGlobalEvent().Emit<RoomDesc*&, int>("RoomManager_getIthRoom", pRm,i);
		if(pRm->owneruin == uin)
		{
			return pRm->isServer;
		}
	}

	return false;
}

bool RoomClient::isProxyOnly(int hostUin)
{
	std::string country = gFunc_getCountry();
	if (m_ProxyOnlyCountrys.find(country) != m_ProxyOnlyCountrys.end())
	{
		// 该国家或地区配置直接走转发
		return true;
	}

	RoomDesc *room = nullptr;
	MNSandbox::GetGlobalEvent().Emit<RoomDesc*&, int>("RoomManager_findRoom", room, hostUin);
	if (!m_isHost && room && room->use_proxy==1)
	{
		// 客机准备加入的房间只走转发
		return true;
	}

	return false;
}

std::string RoomClient::gethttpServer(int roomOwnerUin)
{
	for (int i = 0; i < (int)m_searchUinResults.size(); i++)
	{
		RoomDesc rm = m_searchUinResults[i].second;
		if(rm.owneruin == roomOwnerUin)
		{
			char httpServer[256] = {0};
			sprintf(httpServer, "http://%s:%d", rm.roomIp.c_str(), rm.roomPort);
			return httpServer;
		}
	}


	int roomNum = 0;
	MNSandbox::GetGlobalEvent().Emit<int&>("RoomManager_getNumRoom", roomNum);
	for(int i = 0; i<roomNum; i++)
	{
		RoomDesc * pRm = nullptr;
		MNSandbox::GetGlobalEvent().Emit<RoomDesc*&, int>("RoomManager_getIthRoom", pRm, i);
		if(pRm->owneruin == roomOwnerUin)
		{
			char httpServer[256] = {0};
			sprintf(httpServer, "http://%s:%d", pRm->roomIp.c_str(), pRm->roomPort);
			return httpServer;
		}
	}


	std::string roomHttpServer = getRoomServerUrl(getRoomServerDefault());
	return roomHttpServer;
}

bool RoomClient::getLanRooms(std::string genid)
{
	if (nullptr != mpClientPeer)
		mpClientPeer->Ping("***************", LAN_SERVER_PORT, false);

	PB_GetRoomsInfoRes getRoomsInfoRes;
	getRoomsInfoRes.set_resultcode(PB_ROOM_OP_SUCCESS);

	mpListener->onRSGetRoomsRes(getRoomsInfoRes, genid);

	return true;
}

RakNet::SystemAddress *RoomClient::findHostAddrByUin(int uin)
{
	auto iter = m_RoomHostAddrs.find(uin);
	if(iter != m_RoomHostAddrs.end())
	{
		return &iter->second;
	}
	else return NULL;
}

void RoomClient::addHostAddrByUin(int uin, const char *str, unsigned short port)
{
	m_RoomHostAddrs[uin] = RakNet::SystemAddress(str, port);
}
bool RoomClient::handleRoomMsg(RakNet::Packet *packet, const PB_PACKDATA* packData, size_t len)
{
	if (nullptr == packData || len <= 0) return false;
	if (nullptr == mpListener) return false;

	if (packData->MsgCode == PB_GET_ROOMS_INFO_RES)
	{
		PB_GetRoomsInfoRes getRoomsInfoRes;
		getRoomsInfoRes.ParseFromArray(packData->MsgData, packData->ByteSize);

		mpListener->onRSGetRoomsRes(getRoomsInfoRes, ""); //hrl
		if (m_isLan && getRoomsInfoRes.resultcode() == PB_ROOM_OP_SUCCESS && getRoomsInfoRes.roominfolist_size() > 0)
		{
			m_RoomHostAddrs[(int)getRoomsInfoRes.roominfolist(0).owid()] = packet->systemAddress;
		}
	}

	return true;
}

bool RoomClient::update()
{
	if (m_isLan)
		return updateHeartbeatLan();
	else
		return updateHeartbeatHttp();
}

void RoomClient::setRoomSvrAddr(RakNet::SystemAddress &svrAddr)
{
	mRoomServerAddr = svrAddr;
}

void RoomClient::updateNetCfg(const GameNetCfg &cfg)
{
	mNetCfg = cfg;
}

GameNetCfg RoomClient::getNetCfg()
{
	return mNetCfg;
}

bool RoomClient::disconnect()
{
	if (nullptr != mpClientPeer)
	{
		m_Mutex.Lock();
		RakNet::RakPeerInterface::DestroyInstance(mpClientPeer);
		mpClientPeer = nullptr;
		m_Mutex.Unlock();
	}
	return true;
}

bool RoomClient::isConnected()
{
	return (nullptr != mpClientPeer && 
		mpClientPeer->GetConnectionState(RakNet::UNASSIGNED_SYSTEM_ADDRESS) 
		== RakNet::IS_CONNECTED);
}

void RoomClient::reCreatRoom()
{
	if (m_isHost)
	{
		m_HeartbeatTicks = 0;
		m_HeartbeatRentStartTime = Timer::getSystemTick();
		m_HeartbeatTicksRent = 0;
		m_LastHeartbeatReturn = 0;

#ifndef IWORLD_SERVER_BUILD
		int headicon = GetClientInfoProxy()->getHeadModel();
#else
		int headicon = 1;
#endif
		m_bReCreatRoom = true;

		SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_getGuestMode",
			SandboxContext(nullptr));
		bool guestmode = false;
		if (result.IsExecSuccessed())
		{
			guestmode = result.GetData_Bool();
		}
		reqCreateRoom(mCurrentRoom, headicon, guestmode, m_strFromOwid);
	}
}

bool RoomClient::deleteRoom(int myUin)
{
	mCurrentRoom.OwnerUin = 0;

	if (!isLan())
	{
		return reqCloseRoom(myUin);
	}
	else return true;
}

bool RoomClient::createRoom(int myUin, const ROOMINFO &room, int clientVersion, std::string fromowidstr, std::string game_session_id /*= ""*/)
{
	m_isHost = true;
	m_isLocked = false;

	m_RoomMembers.clear();
	mCurrentRoom = room;
	GetClientInfoProxy()->validateName(mCurrentRoom.RoomTitle);
	GetClientInfoProxy()->validateName(mCurrentRoom.CustomRoomName);

	m_nCurrentRoomOwner = room.OwnerUin;
	m_strFromOwid = fromowidstr;
	if (isLan())
	{
		return updateRoom(myUin, room);
	}
	else
	{
		m_HeartbeatTicks = 0;
		m_HeartbeatRentStartTime = Timer::getSystemTick();
		m_HeartbeatTicksRent = 0;
		m_LastHeartbeatReturn = 0;
#ifndef IWORLD_SERVER_BUILD
		int headicon = GetClientInfoProxy()->getHeadModel();
#else
		int headicon = 1;
#endif

		SandboxResult result = SandboxEventDispatcherManager::GetGlobalInstance().Emit("PermitsSubSystem_getGuestMode",
			SandboxContext(nullptr));
		bool guestmode = false;
		if (result.IsExecSuccessed())
		{
			guestmode = result.GetData_Bool();
		}
		
		return reqCreateRoom(room, headicon,guestmode, fromowidstr);
	}
}

bool RoomClient::updateRoom(int myUin, const ROOMINFO &room)
{
	if(isLan())
	{
		PB_GetRoomsInfoRes getRoomsInfoRes;
		getRoomsInfoRes.set_resultcode(PB_ROOM_OP_SUCCESS);

		PB_RoomInfo* roomInfo = getRoomsInfoRes.add_roominfolist();
		roomInfo->set_owneruin(room.OwnerUin);
		roomInfo->set_playercount(room.PlayerCount);
		roomInfo->set_maxplayercount(room.MaxPlayerCount);
		roomInfo->set_gametype(room.GameType);
		roomInfo->set_gpslatitude(room.GpsLatitude);
		roomInfo->set_gpslongtitude(room.GpsLongtitude);
		roomInfo->set_timestamp(room.Timestamp);
		roomInfo->set_roomtitle(room.RoomTitle);
		roomInfo->set_ownername(room.OwnerName);
		roomInfo->set_password(room.Password);
		roomInfo->set_description(room.Description);
		roomInfo->set_extradata(room.ExtraData);
		roomInfo->set_lastping(room.LastPing);
		roomInfo->set_province(room.Province);
		roomInfo->set_isp(room.Isp);
		roomInfo->set_gamelabel(room.GameLabel);
		roomInfo->set_owid(room.OWID);
		roomInfo->set_regionip(room.RegionIp);
		roomInfo->set_apiid(room.ApiID);
		// wifi房间也一起设置了
		roomInfo->set_maptype(m_strFromOwid);
		roomInfo->set_connectmode(room.ConnectMode);
		roomInfo->set_editorsceneswitch(room.editorSceneSwitch);

		PB_PACKDATA packData(PB_GET_ROOMS_INFO_RES, getRoomsInfoRes.ByteSize());
		getRoomsInfoRes.SerializeToArray(packData.MsgData, packData.ByteSize);
		UDPConnection* roomUdpConn = GetGameNetManagerPtr()->getConnection();
		if (roomUdpConn)
		{
#ifdef BUILD_MINI_EDITOR_APP
			char szPackData[256] = { 0 };
			sprintf_s(szPackData, 255, "%d|%s%d", room.GameLabel, roomInfo->roomtitle().c_str(), roomInfo->roomtitle().length());
			roomUdpConn->setPingResponse(szPackData, sizeof(szPackData));
#else
			roomUdpConn->setPingResponse((const char*)&packData, packData.ByteSize + PB_PROTO_HEAD_LEN);
#endif
		}
	}
	return true;
}

bool RoomClient::getRooms(int myUin, int lastTimestamp, std::string maptype, int host_type/* =0 */, std::string from /*= ""*/, std::string genid/* = ""*/)
{
	m_getRoomListFromFlag = from;
	if(isLan()) return getLanRooms(genid);

	return reqRoomList(1, maptype, host_type,from, genid);
}

bool RoomClient::getRoomsByUins(int myUin, int *uins, int uinCount, int lastTimestamp,std::string from /*= ""*/, std::string genid /*=""*/, int ignoreTimeout/* =0*/)
{
	m_getRoomListFromFlag = from;
	if(isLan()) return getLanRooms(genid);

	return searchUins(uins, uinCount, genid, ignoreTimeout);
}

bool RoomClient::getRoomsByGamelabel(int myUin, int gameType, int lastTimestamp, std::string maptype, int host_type/* =0 */, std::string from /*= ""*/, std::string genid /*= ""*/)
{
	m_getRoomListFromFlag = from;
	if(isLan()) return getLanRooms(genid);

	return reqRoomList(gameType, maptype, host_type, from,genid);
}

void RoomClient::getManorRoomByUin(int destUin)
{
	reqManorRoom(destUin);
}

// ********: 联机组队需求增加相关接口组队模式的传参  codeby:huangrulin
bool RoomClient::joinRoom(int myUin, int roomOwner, const char* passwd, int CanTrace/*=0*/, int connectMode /*= 0*/, std::string genid/*""*/)
{
	m_isHost = false;

	m_nCurrentRoomOwner = roomOwner;

	if(isLan())
	{
		RakNet::SystemAddress* hostAddr = findHostAddrByUin(m_nCurrentRoomOwner);
		if (hostAddr == NULL){
			//		ROOM_DEBUG("hostaddr is null.")
			char szErrDetails[128];
			snprintf(szErrDetails, sizeof(szErrDetails), "Room Process Error At:(%d) - %s", __LINE__, "hostaddr is null.");
			MINIW::ScriptVM::game()->callFunction("RoomProcessErrorLog", "s", szErrDetails);
			return false;
		}

		mRoomServerAddr = *hostAddr;

		PB_JoinRoomRes joinRoomRes;
		joinRoomRes.set_result(PB_JOIN_ROOM_SUCCESS);
		mpListener->onJoinRoomReqRes(joinRoomRes, genid);

		return true;
	}
	else
	{
		// ********: 联机组队需求增加相关接口组队模式的传参  codeby:huangrulin
		return reqJoinRoom(myUin, roomOwner, passwd, genid, CanTrace, connectMode);
	}
}

bool RoomClient::joinRentRoom(const char* serverid, const char *passwd, std::string genid)
{
	// 这里因为服务器还没弄好 就直接返回一个成功的 0
	MNSandbox::GetGlobalEvent().Emit<int,std::string,const char*>("RoomManager_onRespJoinRentRoomHttp", 0, genid, serverid);
	return true;
}
bool RoomClient::leaveRoom(int myUin, int roomOwner, int cause)
{
	GetGameNetManagerPtr()->leaveRoom(myUin);
	m_nCurrentRoomOwner = 0;
	if (GetGameNetManagerPtr()->isServerRoom())
	{
		// 云服房间不用调 leaveroom 接口
		return true;
	}
	else
	{
		return reqLeaveRoom(myUin, roomOwner, cause);
	}
}

bool RoomClient::updateRoomFlags(int myUin, int nStage)
{
	if (mCurrentRoom.OwnerUin == 0)
	{
		return false;
	}

	if (!isLan())
	{
		// 由主机发送心跳消息
		reqHostUpdateRoom(myUin, nStage);
	}
	updateNearbyLanRoom(mCurrentRoom);
	return updateRoom(myUin, mCurrentRoom);
}

bool RoomClient::updateRoomIncPlayer(int myUin, int memberuin)
{	
	LOG_INFO( "@ updateRoomIncPlayer = [%d]", myUin );
	if (mCurrentRoom.OwnerUin == 0)
	{
		return false;
	}

	if(m_RoomMembers.find(memberuin) != m_RoomMembers.end())
	{
		m_StayRoomMembers.insert(memberuin);  // 玩家已存在, 说明是上一局遗留的玩家发送进入游戏
		if (!isLan())
		{
			// 由主机发送心跳消息
			reqHostUpdateRoom(myUin);
		}

		return true;
	}
	m_RoomMembers.insert(memberuin);

	mCurrentRoom.PlayerCount++;

	if (!isLan())
	{
		// 由主机发送心跳消息
		reqHostUpdateRoom(myUin);
	}

	updateNearbyLanRoom(mCurrentRoom);

	return updateRoom(myUin, mCurrentRoom);
}

bool RoomClient::updateRoomDecPlayer(int myUin, int memberuin)
{
	if (mCurrentRoom.OwnerUin == 0)
	{
		return false;
	}

	auto iter = m_RoomMembers.find(memberuin);
	if(iter == m_RoomMembers.end())
	{
		if (!isLan())
		{
			//由主机发送心跳消息
			reqHostUpdateRoom(myUin);
		}

		return true;
	}
	m_RoomMembers.erase(iter);

	mCurrentRoom.PlayerCount--;
	m_StayRoomMembers.erase(memberuin);

	if (!isLan())
	{
		// 由主机发送心跳消息
		reqHostUpdateRoom(myUin);
	}

	if (mCurrentRoom.PlayerCount < 1) return false;

	updateNearbyLanRoom(mCurrentRoom);
	return updateRoom(myUin, mCurrentRoom);
}
/////////////////////////////////////////////////////////////////////////////////////////

GameNetCfg* RoomClient::getRoomServerDefault()
{
	return &mNetCfg;
}

GameNetCfg* RoomClient::getRoomServerByUin(int uin)
{
	auto it = m_uinRoomServerCfg.find(uin);  //room owner is in another roomserver
	if (it != m_uinRoomServerCfg.end())
		return &it->second;
	else
		return NULL;
}

std::string RoomClient::getRoomServerUrl(const GameNetCfg* cfg, int read_config)
{
	if (cfg == NULL){
		cfg = getRoomServerDefault();
	}

	if(cfg == NULL){	
		int apiid = GetClientInfoProxy()->GetAppId();
		if(apiid == 303 || apiid == 310){
			return  std::string("http://**************:8080");
		}else{
			return  std::string("http://*************:8080");
		}
	} 
	static Rainbow::NoFreeFixedString game_env("game_env");
	int env = GetIWorldConfigProxy()->getGameData(game_env);
	if (env >= 10 && read_config == 0)
	{
		std::string roomServer = GetIWorldConfigProxy()->GetUrlString("RoomHostHttp");
		roomServer.pop_back();//去掉最后的'/'
		return roomServer;
	}
	else
	{
		char buf[256];
#ifndef IWORLD_SERVER_BUILD
	std::sprintf(buf, "http://%s:%d", cfg->roomhosthttp_url.c_str(), cfg->roomhosthttp_port);
#else
	std::sprintf(buf, "http://%s:%d", GetClientInfoProxy()->GetRoomServerUrl().c_str(), 8080);
#endif
	return std::string(buf);
	}
}

std::string RoomClient::getRoomServer()
{
	return getRoomServerUrl(getRoomServerDefault());
}
#if ROOM_USE_NEW_HTTPMANAGER
#else
void RoomClient::downloadProgress(int taskid, int progress) {

	if (progress < 100 && progress >= 0)
		return;

	std::map<int, std::pair<ReqType, std::string>>::iterator it = m_httpRequests.find(taskid);
	if (it != m_httpRequests.end())
	{
		
		int result = (progress < 0) ? progress : 0;

		jsonxx::Object* jsonobj = NULL;
		if (result == 0)
		{
			std::string content;
			if (HttpDownloadMgr::GetInstance().getHttpContentSize(taskid) > 0)
				content = HttpDownloadMgr::GetInstance().getHttpContentStr(taskid);

			jsonobj = new jsonxx::Object;
			if (!jsonobj->parse(content))
			{				
				result = RoomErr_JsonParseFail;
			}

			if (result == 0)
			{
				if (jsonobj->has<jsonxx::Number>("result"))
					result = (int)jsonobj->get<jsonxx::Number>("result");
				else
					result = RoomErr_MissingResult;
			}
		}

		ReqType type = (it->second).first;
		std::string genid = (it->second).second;
		
		if (type == RT_LoginRoomServer)
			respLoginRoomServer(result, jsonobj, genid);
		else if (type == RT_RoomList)
			respRoomList(result, jsonobj, genid);
		else if (type == RT_SearchUinRoom)
			respSearchUinRoom(result, jsonobj, genid);
		else if (type == RT_GetUinRoom)
			respGetUinRoom(result, jsonobj, genid);
		else if (type == RT_CreateRoom)
			respCreateRoom(result, jsonobj, genid);
		else if (type == RT_CloseRoom)
			respCloseRoom(result, jsonobj);
		else if (type == RT_JoinRoom)
			respJoinRoom(result, jsonobj, genid);
		else if (type == RT_LeaveRoom)
			respLeaveRoom(result, jsonobj);
		else if (type == RT_Heartbeat)
			respHeartbeat(result, jsonobj);
		else if (type == RT_RoomMember)
			respReportRoomMembers(result, jsonobj);
		else if (type == RT_HotList)
			respHotRoomList(result, jsonobj);
		else if (type == RT_ReEnterRoom)
			respReEnterRoom(result, jsonobj, genid);
		else if (type == RT_MemberExist)
			respCheckMemberExist(result, jsonobj);
		else if (type == RT_NearByRoomSearch)
			respSearchNearByUinRoom(result, jsonobj);
		else if (type == RT_GetNearByUinRoom)
			respGetNearByUinRoom(result, jsonobj);
		else if (type == RT_HostUpdateRoom)
			respHostUpdateRoom(result, jsonobj);
		else if (type == RT_SearchManorUinRoom)
			respManorRoom(result, jsonobj);
		else if (type == RT_GetManorUinRoom)
			respGetManorUinRoom(result, jsonobj);

		ENG_DELETE(jsonobj);
		m_httpRequests.erase(taskid);
		
	}
}
#endif

//void RoomClient::onGameEvent(GameEvent *ge)
//{
//#if ROOM_USE_NEW_HTTPMANAGER
//#else
//	if (ge->getype == GE_HTTP_DOWNLOAD_PROGRESS)
//	{
//		int taskid = ge->body.httpprogress.task_id;
//		int progress = ge->body.httpprogress.progress;
//		downloadProgress(taskid, progress);
//		
//	}
//#endif
//}

void RoomClient::parseRoomDesc(const jsonxx::Object& src, RoomDesc& rm)
{
	using namespace jsonxx;

	bool isguestmode = (src.get<Number>("right", 1) == 2);  //TODO

	rm.owneruin			= (int)src.get<Number>("uin", 0);
	rm.ownericon		= (int)src.get<Number>("uicon", 1);
	rm.ownericonframe	= (int)src.get<Number>("uicon_box", 1);
	rm.hasAvatar		= (int)src.get<Number>("has_avatar", 0);
	rm.nplayers			= (int)src.get<Number>("cur_count", 1);
	rm.maxplayers		= (int)src.get<Number>("max_count", 6);
	rm.gametype			= (int)src.get<Number>("room_type", 0);
	rm.createtime		= (int)src.get<Number>("create_time", 0);

	rm.roomname = src.get<MINIW::String>("room_name", "");
	rm.ownername = src.get<MINIW::String>("uname", "");
	rm.desc = src.get<MINIW::String>("desc", "");
	rm.isCollect = false;  //set in ClientAccountMgr
	rm.password = (src.get<Number>("passwd", 0) == 1) ? "***" : "";  //1=有密码，0=无密码
	rm.extraData = src.get<MINIW::String>("extra_data", "{}");
	jsonxx::Object txtObj;
	if (txtObj.parse(rm.extraData) && txtObj.has<jsonxx::Number>("editorSceneSwitch"))
	{
		rm.editorSceneSwitch = (int)txtObj.get<jsonxx::Number>("editorSceneSwitch");
	}

	rm.lastPing		= (int)src.get<Number>("ping", 500);
	rm.province		= (int)src.get<Number>("net_area", 0);
	rm.isp			= (int)src.get<Number>("net_isp", 0);
	rm.gamelabel	= (int)src.get<Number>("game_label", 8);
	rm.owid = 0;
	rm.gotpic = false;
	rm.ApiID	= (int)src.get<Number>("device", 1);
	rm.locked	= (int)src.get<Number>("locked", 0);

	if (src.has<Number>("host_type") && (src.get<Number>("host_type") == 1))
	{
		rm.isServer = src.get<Number>("host_type", 0) == 1;
		rm.serverIp = src.get<MINIW::String>("server_ip", "");
		rm.serverPort = atoi(src.get<MINIW::String>("server_port","").c_str());
		addHostAddrByUin(rm.owneruin, rm.serverIp.c_str(), rm.serverPort);
	}

	rm.roomIp = src.get<MINIW::String>("room_ip", "");
	rm.roomPort = (int)src.get<Number>("room_port",0);
		
	rm.owid_md5 = src.get<MINIW::String>("map_id", "0");  //seems useless
	rm.thumbnail_url = src.get<MINIW::String>("thumbnail", "");  //e.g."http://map2.mini1.cn/map/2/time/8464f4d8207d0e09950c308494495f31"

	rm.map_type = src.get<MINIW::String>("map_type", "");  //地图owid
	rm.game_stage = (int)src.get<Number>("stage", 0);	//游戏状态

	rm.isnearby = 0;	// 是否是附近玩家
	MINIW::String::size_type lastSlashPos = rm.thumbnail_url.rfind('/');
	MINIW::String::size_type tailPos = rm.thumbnail_url.rfind('.');
	if(tailPos <= lastSlashPos) tailPos = MINIW::String::npos;

	if(tailPos != MINIW::String::npos) rm.thumbnail_ext = rm.thumbnail_url.substr(tailPos);
	else rm.thumbnail_ext = ".png";

	if (lastSlashPos != MINIW::String::npos && lastSlashPos + 1 < rm.thumbnail_url.size())
		rm.thumbnail_md5 = rm.thumbnail_url.substr(lastSlashPos + 1, tailPos-lastSlashPos-1);
	else
		rm.thumbnail_md5 = "";

	rm.connect_mode = (int)src.get<Number>("connect_mode", 0);
	rm.use_proxy = (int)src.get<Number>("use_proxy", 0);
	rm.public_type = (int)src.get<Number>("public_type", 0);
	rm.owner_can_trace = (int)src.get<Number>("owner_can_trace", 0x0fffffff);
}

void RoomClient::parseHotRoomDesc(const jsonxx::Object& src, HotRoomDesc& hotrm)
{
	using namespace jsonxx;

	hotrm.owid = src.get<MINIW::String>("id", "");
	hotrm.owname = src.get<MINIW::String>("name", "");
	hotrm.count = (int)src.get<Number>("count", 0);
	hotrm.mark	= (int)src.get<Number>("mark", 0);
	hotrm.thumbnailurl = src.get<MINIW::String>("url", "");
	hotrm.thumbnailmd5 = src.get<MINIW::String>("md5", "");
}

void RoomClient::HandleRequestEnd(bool success, Rainbow::Http::WebRequest* request)
{
#if ROOM_USE_NEW_HTTPMANAGER
	ROOM_TASK_DATA* task_data = reinterpret_cast<ROOM_TASK_DATA*>(request->GetUserData());
	Assert(task_data);
	if (!task_data) return;
	if (task_data->room_client == nullptr) {
		ENG_DELETE_LABEL(task_data, kMemGame);
		// TODO: how to do here, return will make bad UE
		return;
	}

	RoomClient* room_client = task_data->room_client;	
	//std::map<int, std::pair<ReqType, std::string>>::iterator it = room_client->m_httpRequests.find(taskid);
	//if (it != room_client->m_httpRequests.end())
	{
		int httpCode = request->GetHttpCode();
		int result = 0;
		bool succeed = false;
		ReqType type = task_data->req_type;
		std::string genid = task_data->genid;
		// Note by LiuQi(<EMAIL>)
		// We assume that all the succeed HTTP API response code equal to 200 or 204, most of time, it's enough.
		if (200 == httpCode || 204 == httpCode) {
			succeed = true;
		}

		// Parse json
		std::string content = request->GetResponse();
		jsonxx::Object* jsonobj = ENG_NEW(jsonxx::Object)();
		bool parseRet = jsonobj->parse(content);
		if (!parseRet)
		{
			ENG_DELETE(jsonobj);
			result = RoomErr_JsonParseFail;
		}
		if (parseRet) {
			if (jsonobj->has<jsonxx::Number>("result"))
				result = (int)jsonobj->get<jsonxx::Number>("result");
			else
				result = RoomErr_MissingResult;
		}

		if (!succeed) {
			// If request failed, call ShowGameTips for user and returns.
			//ge GetGameEventQue().postRSError(type, httpCode);
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GIE_RS_ERROR", MNSandbox::SandboxContext(nullptr).
					SetData_Number("type", type).
					SetData_Number("code", httpCode));
			}
		}

		// Handle concrete response
		if (type == RT_LoginRoomServer)
			room_client->respLoginRoomServer(result, jsonobj, genid);
		else if (type == RT_RoomList)
			room_client->respRoomList(result, jsonobj, genid);
		else if (type == RT_SearchUinRoom)
			room_client->respSearchUinRoom(result, jsonobj, genid);
		else if (type == RT_GetUinRoom)
			room_client->respGetUinRoom(result, jsonobj, genid);
		else if (type == RT_CreateRoom)
			room_client->respCreateRoom(result, jsonobj, genid);
		else if (type == RT_CloseRoom)
			room_client->respCloseRoom(result, jsonobj);
		else if (type == RT_JoinRoom)
			room_client->respJoinRoom(result, jsonobj, genid);
		else if (type == RT_LeaveRoom)
			room_client->respLeaveRoom(result, jsonobj);
		else if (type == RT_Heartbeat)
			room_client->respHeartbeat(result, jsonobj);
		else if (type == RT_RoomMember)
			room_client->respReportRoomMembers(result, jsonobj);
		else if (type == RT_HotList)
			room_client->respHotRoomList(result, jsonobj);
		else if (type == RT_ReEnterRoom)
			room_client->respReEnterRoom(result, jsonobj, genid);
		else if (type == RT_MemberExist)
			room_client->respCheckMemberExist(result, jsonobj);
		else if (type == RT_NearByRoomSearch)
			room_client->respSearchNearByUinRoom(result, jsonobj);
		else if (type == RT_GetNearByUinRoom)
			room_client->respGetNearByUinRoom(result, jsonobj);
		else if (type == RT_HostUpdateRoom)
			room_client->respHostUpdateRoom(result, jsonobj);
		else if (type == RT_SearchManorUinRoom)
			room_client->respManorRoom(result, jsonobj);
		else if (type == RT_GetManorUinRoom)
			room_client->respGetManorUinRoom(result, jsonobj);

		ENG_DELETE(jsonobj);
	}

	room_client->RemoveHttptask(task_data->task_id);
	ENG_DELETE_LABEL(task_data, kMemGame);
#endif
}

bool RoomClient::reqLoginRoomServer(std::string genid)
{
	LOG_INFO("@ reqLoginRoomServer");

#ifdef IWORLD_SERVER_BUILD
	//租赁服未配置roomserverip
	if (GetGameInfoProxy()->GetRoomHostType() == ROOM_SERVER_RENT)
	{
		if (!m_hasRoomServerIp )
		{
			//ge GetGameEventQue().postRSConnect(3, genid);
			MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
				SetData_Number("result", 3).
				SetData_String("genid", genid).
				SetData_Number("detailreason", -1);
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GIE_RSCONNECT_RESULT", sandboxContext);
			}
			return true;
		}
	}
#endif

	if (s_serverConfigPulled && !IsConfigPulledOutTime())
	{
		MNSandbox::GetGlobalEvent().Emit<int, int, int, int, int, std::string >("RoomManager_onGetServerInfoHttp", s_latestver, s_maxver, s_minver, s_province, s_isp, s_roomZone);
		//ge GetGameEventQue().postRSConnect(3, genid);
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("result", 3).
			SetData_String("genid", genid).
			SetData_Number("detailreason", -1);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GIE_RSCONNECT_RESULT", sandboxContext);
		}
		return true;
	}

	m_uinRoomServerCfg.clear();

	std::string url =
		RoomReqBuilder(getRoomServerUrl(getRoomServerDefault()), "/server/room")
		.addParam("cmd", "server_config")
		.addParam("uin", GetClientInfoProxy()->getUin())
		.end();

	LOG_INFO("  url = '%s'", url.c_str());

#if ROOM_USE_NEW_HTTPMANAGER
	UInt32 taskid = ++s_TaskID;
	ROOM_TASK_DATA* task_data = ENG_NEW_LABEL(ROOM_TASK_DATA, kMemGame) (taskid, RT_LoginRoomServer, genid, this);
	m_httptask_array[taskid] = Http::GetHttpManager().Request(url, "", task_data, 0, nullptr, HandleRequestEnd);
	//s_RoomTaskArray[this].emplace_back(task_data);
#else
	int taskid = HttpDownloadMgr::GetInstance().rpc(url, "", 0, true);
	// UNDONE
	//GetHttpManager().RPC(url, "", nullptr, 1, nullptr, [](bool isOK,WebRequest* task) {
	//	jsonxx::Object jsonResult;
	//	int result = ParseJsonObj(task->GetResponse().c_str(), jsonResult);
	//	respLoginRoomServer(result, jsonobj, genid);
	//});
	m_httpRequests.insert(std::make_pair(taskid, std::make_pair(RT_LoginRoomServer, genid)));
#endif

	m_lastPingSendTime = Rainbow::Timer::getSystemTick();

	return true;
}

void RoomClient::respLoginRoomServer(int result, jsonxx::Object* jsonobj, std::string genid)
{
	LOG_INFO("respLoginRoomServer %d", result);

	using namespace jsonxx;

	if (RoomErr_NoErr == result)
	{
		m_lastPing = Rainbow::Timer::getSystemTick() - m_lastPingSendTime;
		//登录成功根据国家码重新获取roomhosthttp_url	
		mNetCfg.roomhosthttp_url = GetIWorldConfigProxy()->GetClientUrl("RoomHostHttp");
		mNetCfg.roomhosthttp_port = GetIWorldConfigProxy()->GetClientUrlPort("RoomHostHttp");
		if (jsonobj->has<jsonxx::Object>("config"))
		{
			const jsonxx::Object& confignode = jsonobj->get<jsonxx::Object>("config");

			if (confignode.has<jsonxx::Object>("punch"))
			{
				const jsonxx::Object& punchnode = confignode.get<jsonxx::Object>("punch");

				mNetCfg.natpunch_url = punchnode.get<MINIW::String>("ip", "0.0.0.0");
				mNetCfg.natpunch_port = (int)punchnode.get<Number>("port", 0);
			}

			if (confignode.has<jsonxx::Object>("proxy"))
			{
				const jsonxx::Object& proxynode = confignode.get<jsonxx::Object>("proxy");

				mNetCfg.proxy_url = proxynode.get<MINIW::String>("ip", "0.0.0.0");
				mNetCfg.proxy_port = (int)proxynode.get<Number>("port", 0);
			}

			if (confignode.has<jsonxx::Object>("room"))
			{
				const jsonxx::Object& roomnode = confignode.get<jsonxx::Object>("room");

				mNetCfg.roomhosthttp_url = roomnode.get<MINIW::String>("ip", "0.0.0.0");
				mNetCfg.roomhosthttp_port = (int)roomnode.get<Number>("port", 0);
			}
			mNetCfg.network_type = (int)confignode.get<Number>("network_type", 0);

			std::string proxyOnly = confignode.get<MINIW::String>("proxy_only", "");
			std::string maxVerStr = confignode.get<MINIW::String>("maxVersion", "0.0.0");
			std::string minVerStr = confignode.get<MINIW::String>("minVersion", "0.0.0");
			std::string latestVerStr = confignode.get<MINIW::String>("latestVersion", "0.0.0");
			//海外联机设置大区类型
			s_roomZone = confignode.get<MINIW::String>("block_type","");

			m_ProxyOnlyCountrys.clear();
			std::vector<std::string> vCountry;
			Rainbow::StringUtil::split(vCountry, proxyOnly, ",");
			int sizeCountry = vCountry.size();
			for(int i =0 ; i < sizeCountry; i++)
			{
				m_ProxyOnlyCountrys.insert(vCountry[i]);
			}
			
			s_maxver = GetClientInfoProxy()->clientVersionFromStr(maxVerStr.c_str());
			s_minver = GetClientInfoProxy()->clientVersionFromStr(minVerStr.c_str());
			s_latestver = GetClientInfoProxy()->clientVersionFromStr(latestVerStr.c_str());

			s_province = 0;
			s_isp = 0;

			MarkConfigPulled();

			GetGameNetManagerPtr()->updateNetCfg(mNetCfg);
			MNSandbox::GetGlobalEvent().Emit<int, int, int, int, int, std::string >("RoomManager_onGetServerInfoHttp", s_latestver, s_maxver, s_minver, s_province, s_isp, s_roomZone);
		}
		MNSandbox::GetGlobalEvent().Emit<const int, bool>("RoomManager_onLastPing", m_lastPing, false);
	}

	//ge GetGameEventQue().postRSConnect(3, genid);
	MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
		SetData_Number("result", 3).
		SetData_String("genid", genid).
		SetData_Number("detailreason", -1);
	if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
		MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GIE_RSCONNECT_RESULT", sandboxContext);
	}
}

bool RoomClient::reqRoomList(int gamelabel /* = 1 */, std::string maptype/* = */, int host_type/* =0 */, std::string from/*= ""*/, std::string genid/*""*/)
{
	// 这里用开关进行控制 是否显示附近房间
	bool ret = false;
	MNSandbox::GetGlobalEvent().Emit<bool& >("RoomManager_getNearbySwitch",ret);
	if (ret)
	{
		if (nullptr != mpClientPeer)
			mpClientPeer->Ping("***************", LAN_CLIENT_PORT, false);
	}

	m_uinRoomServerCfg.clear();

	std::string url =
		RoomReqBuilder(getRoomServerUrl(getRoomServerDefault()), "/server/room")
		.addParam("cmd", "query_room_list")
		.addParam("country", gFunc_getCountry())
		.addParam("game_label", gamelabel)
		.addParam("host_type", host_type)
		.addParam("map_type", maptype)
		.addParam("uin", GetClientInfoProxy()->getUin())
		.addParam("version", GetClientInfoProxy()->GetClientVersionStr())
		//.addParam("from", from)
		.end();

	LOG_INFO(" @ reqRoomList url = '%s'", url.c_str());

	//海外查询主机房间列表添加语言参数，不参与签名
	char tmp[64] = { 0 };
	std::sprintf(tmp, "&lang=%d", GetIWorldConfigProxy()->getGameData("lang"));
	url.append(tmp);

#if ROOM_USE_NEW_HTTPMANAGER
	UInt32 taskid = ++s_TaskID;
	ROOM_TASK_DATA* task_data = ENG_NEW_LABEL(ROOM_TASK_DATA, kMemGame) (taskid, RT_RoomList, genid, this);
	
	m_httptask_array[taskid] = Http::GetHttpManager().Request(url, "", task_data, 0, nullptr, HandleRequestEnd);
	//s_RoomTaskArray[this].emplace_back(task_data);
#else
	int taskid = HttpDownloadMgr::GetInstance().rpc(url, "", 0, true);

	m_httpRequests.insert(std::make_pair(taskid, std::make_pair(RT_RoomList, genid)));
#endif	

	m_lastPingSendTime = Rainbow::Timer::getSystemTick();

	return true;
}

void RoomClient::respRoomList(int result, jsonxx::Object* jsonobj, std::string genid)
{
	LOG_INFO("respRoomList %d", result);
	using namespace jsonxx;

	if (RoomErr_NoErr == result)
	{
		m_lastPing = Rainbow::Timer::getSystemTick() - m_lastPingSendTime;

		if (jsonobj->has<jsonxx::Object>("config"))
		{
			const jsonxx::Object& confignode = jsonobj->get<jsonxx::Object>("config");

			if (confignode.has<jsonxx::Object>("punch"))
			{
				const jsonxx::Object& punchnode = confignode.get<jsonxx::Object>("punch");

				mNetCfg.natpunch_url = punchnode.get<MINIW::String>("ip", "0.0.0.0");
				mNetCfg.natpunch_port = (int)punchnode.get<Number>("port", 0);
			}

			if (confignode.has<jsonxx::Object>("proxy"))
			{
				const jsonxx::Object& proxynode = confignode.get<jsonxx::Object>("proxy");

				mNetCfg.proxy_url = proxynode.get<MINIW::String>("ip", "0.0.0.0");
				mNetCfg.proxy_port = (int)proxynode.get<Number>("port", 0);
			}

			if (confignode.has<jsonxx::Object>("room"))
			{
				const jsonxx::Object& roomnode = confignode.get<jsonxx::Object>("room");

				mNetCfg.roomhosthttp_url = roomnode.get<MINIW::String>("ip", "0.0.0.0");
				mNetCfg.roomhosthttp_port = (int)roomnode.get<Number>("port", 0);
			}

			std::string proxyOnly = confignode.get<MINIW::String>("proxy_only", "");
			std::string maxVerStr = confignode.get<MINIW::String>("maxVersion", "0.0.0");
			std::string minVerStr = confignode.get<MINIW::String>("minVersion", "0.0.0");
			std::string latestVerStr = confignode.get<MINIW::String>("latestVersion", "0.0.0");

			m_ProxyOnlyCountrys.clear();
			std::vector<std::string> vCountry;
			Rainbow::StringUtil::split(vCountry, proxyOnly, ",");
			int sizeCountry = vCountry.size();
			for(int i =0 ; i < sizeCountry; i++)
			{
				m_ProxyOnlyCountrys.insert(vCountry[i]);
			}

			s_maxver = GetClientInfoProxy()->clientVersionFromStr(maxVerStr.c_str());
			s_minver = GetClientInfoProxy()->clientVersionFromStr(minVerStr.c_str());
			s_latestver = GetClientInfoProxy()->clientVersionFromStr(latestVerStr.c_str());

			s_province = 0;
			s_isp = 0;
			
			GetGameNetManagerPtr()->updateNetCfg(mNetCfg);
			MNSandbox::GetGlobalEvent().Emit<int, int, int, int, int, std::string >("RoomManager_onGetServerInfoHttp", s_latestver, s_maxver, s_minver, s_province, s_isp, s_roomZone);
		}
		
		std::vector<RoomDesc> rooms;
		if (jsonobj->has<jsonxx::Array>("list"))
		{
			const jsonxx::Array& listnode = jsonobj->get<jsonxx::Array>("list");

			for (int i = 0; i < (int)listnode.size(); i++)
			{
				const jsonxx::Object& src = listnode.get<jsonxx::Object>(i);

				RoomDesc rm;
				parseRoomDesc(src, rm);

				rm.from_http = true;

				rooms.push_back(rm);
			}
		}
		MNSandbox::GetGlobalEvent().Emit<int, std::string, RoomDesc*, int, std::string>("RoomManager_onGetRoomListHttp", result, genid, rooms.data(), rooms.size(), m_getRoomListFromFlag);
	}
	else
	{
		MNSandbox::GetGlobalEvent().Emit<int, std::string, RoomDesc*, int, std::string>("RoomManager_onGetRoomListHttp", result, genid, NULL, 0, m_getRoomListFromFlag);
	}

	MNSandbox::GetGlobalEvent().Emit<const int, bool>("RoomManager_onLastPing", m_lastPing, false);
}

bool RoomClient::searchUins(int* uins, int num, std::string genid, int ignoreTimeout)
{
	LOG_INFO("@ searchUins");

	m_searchUins.clear();
	m_searchUinResults.clear();
	
	m_uinRoomServerCfg.clear();

	for (int i = 0; i < num; i++)
	{
		int uin = uins[i];
		m_searchUins.push_back(uin);
	}

	reqSearchNextUin(genid, ignoreTimeout);

	return true;
}

void RoomClient::searchUinFinish(std::string genid)
{
	LOG_INFO("@ searchUinFinish");

	if (m_searchUinResults.size() == 1)
	{
		int result = m_searchUinResults[0].first;
		RoomDesc rm = m_searchUinResults[0].second;

		if (result == 0) 
		{
			MNSandbox::GetGlobalEvent().Emit<int, std::string, RoomDesc*, int, std::string>("RoomManager_onGetRoomListHttp", result, genid, &rm, 1, m_getRoomListFromFlag);
		}
		else
		{
			MNSandbox::GetGlobalEvent().Emit<int, std::string, RoomDesc*, int, std::string>("RoomManager_onGetRoomListHttp", result, genid, NULL, 0, m_getRoomListFromFlag);
		}
	}
	else
	{
		std::vector<RoomDesc> rooms;
		for (int i = 0; i < (int)m_searchUinResults.size(); i++)
		{
			int result = m_searchUinResults[i].first;
			RoomDesc rm = m_searchUinResults[i].second;
			if (result == 0)
				rooms.push_back(rm);
		}

		if (rooms.size() > 0)
		{
			MNSandbox::GetGlobalEvent().Emit<int, std::string, RoomDesc*, int, std::string>("RoomManager_onGetRoomListHttp", 0, genid, rooms.data(), rooms.size(), m_getRoomListFromFlag);
		}
		else
		{
			MNSandbox::GetGlobalEvent().Emit<int, std::string, RoomDesc*, int, std::string>("RoomManager_onGetRoomListHttp", RoomErr_no_room, genid, NULL, 0, m_getRoomListFromFlag);
		}
	}
	MNSandbox::GetGlobalEvent().Emit<const int, bool>("RoomManager_onLastPing", m_lastPing, true);
}

void RoomClient::reqSearchNextUin(std::string genid, int ignoreTimeout/* =0*/)
{
	if (m_searchUins.empty())
	{
		searchUinFinish(genid);
		return;
	}

	int uin = m_searchUins[0];
	m_searchUins.erase(m_searchUins.begin());

	LOG_INFO("@ reqSearchNextUin: %d", uin);
	m_CurrentSearchUin = uin;
	if (uin < 1000)
	{
		m_searchUinResults.push_back(std::make_pair(RoomErr_InvalidUin, RoomDesc()));
		return;
	}

	std::string url =
		RoomReqBuilder(getRoomServerUrl(getRoomServerDefault()), "/server/room")
		.addParam("cmd", "query_role_room")
		.addParam("des_uin", uin)
		.addParam("src_uin", GetClientInfoProxy()->getUin())
		.end();
	url += "&";
	url += "ignore_room_timeout=";
	url += std::to_string(ignoreTimeout);
	LOG_INFO("  url = '%s'", url.c_str());

#if ROOM_USE_NEW_HTTPMANAGER
	UInt32 taskid = ++s_TaskID;
	ROOM_TASK_DATA* task_data = ENG_NEW_LABEL(ROOM_TASK_DATA, kMemGame) (taskid, RT_SearchUinRoom, genid, this);
	
	m_httptask_array[taskid] = Http::GetHttpManager().Request(url, "", task_data, 0, nullptr, HandleRequestEnd);

	//s_RoomTaskArray[this].emplace_back(task_data);
#else
	int taskid = HttpDownloadMgr::GetInstance().rpc(url);

	m_httpRequests.insert(std::make_pair(taskid, std::make_pair(RT_SearchUinRoom, genid)));
#endif

	m_lastPingSendTime = Rainbow::Timer::getSystemTick();
}

void RoomClient::respSearchUinRoom(int result, jsonxx::Object* jsonobj, std::string genid)
{
	LOG_INFO("respSearchUinRoom %d", result);

	using namespace jsonxx;

	bool succeed = false;
	bool searchNextUin = true;

	if (RoomErr_NoErr == result)  //在本服
	{
		m_lastPing = Rainbow::Timer::getSystemTick() - m_lastPingSendTime;

		if (jsonobj != NULL && jsonobj->has<jsonxx::Object>("room_info"))
		{
			const jsonxx::Object& roomnode = jsonobj->get<jsonxx::Object>("room_info");

			RoomDesc rm;
			parseRoomDesc(roomnode, rm);

			succeed = true;

			rm.from_http = true;

			m_searchUinResults.push_back(std::make_pair(0, rm));
		}
		else
			result = RoomErr_MissingParams;
	}
	else if (RoomErr_other_room == result)  //在他服
	{
		m_lastPing = Rainbow::Timer::getSystemTick() - m_lastPingSendTime;

		succeed = true;
		searchNextUin = false;				

		if (jsonobj != NULL && jsonobj->has<Number>("uin") && jsonobj->has<Number>("port") && 
			jsonobj->has<MINIW::String>("ip") &&
			jsonobj->has<Number>("port") &&
			//jsonobj->has<MINIW::String>("co_ip") &&
			//jsonobj->has<Number>("co_port") &&
			jsonobj->has<MINIW::String>("p_ip") &&
			jsonobj->has<Number>("p_port") &&
			jsonobj->has<MINIW::String>("pr_ip") &&
			jsonobj->has<Number>("pr_port"))
		{
			GameNetCfg uinNetCfg;
			uinNetCfg.roomhosthttp_url = jsonobj->get<MINIW::String>("ip");
			uinNetCfg.roomhosthttp_port = (int)jsonobj->get<Number>("port");
			uinNetCfg.natpunch_url = jsonobj->get<MINIW::String>("p_ip");
			uinNetCfg.natpunch_port = (int)jsonobj->get<Number>("p_port");
			uinNetCfg.proxy_url = jsonobj->get<MINIW::String>("pr_ip");
			uinNetCfg.proxy_port = (int)jsonobj->get<Number>("pr_port");

			int uin = (int)jsonobj->get<Number>("uin");
			m_uinRoomServerCfg[uin] = uinNetCfg;

			reqGetUinRoom(uin, uinNetCfg, genid);
		}
		else
		{
			LOG_INFO("respSearchUinRoom: failed, json missing params");
		}
	}

	if (!succeed)
	{
		m_searchUinResults.push_back(std::make_pair(result, RoomDesc()));
		MNSandbox::GetGlobalEvent().Emit<int>("ClientAccountMgr_delCollectUin", m_CurrentSearchUin);
	}
	else
	{
		MNSandbox::GetGlobalEvent().Emit<>("RoomManager_clearNearbyRoomList");
	}

	if (searchNextUin)
		reqSearchNextUin(genid);
}

void RoomClient::reqGetUinRoom(int uin, const GameNetCfg& cfg, std::string genid)
{
	LOG_INFO("@ reqGetUinRoom: %d ip='%s', port=%d", uin, cfg.roomhosthttp_url.c_str(), cfg.roomhosthttp_port);
	
	std::string url =
		RoomReqBuilder(getRoomServerUrl(&cfg, 1), "/server/room")
		.addParam("cmd", "query_room_info")
		.addParam("des_uin", uin)
		.addParam("src_uin", GetClientInfoProxy()->getUin())
		.end();

	LOG_INFO("  url = '%s'", url.c_str());

#if ROOM_USE_NEW_HTTPMANAGER
	UInt32 taskid = ++s_TaskID;

	ROOM_TASK_DATA* task_data = ENG_NEW_LABEL(ROOM_TASK_DATA, kMemGame) (taskid, RT_GetUinRoom, genid, this);
	
	m_httptask_array[taskid] = Http::GetHttpManager().Request(url, "", task_data, 0, nullptr, HandleRequestEnd);

	//s_RoomTaskArray[this].emplace_back(task_data);
#else
	int taskid = HttpDownloadMgr::GetInstance().rpc(url);

	m_httpRequests.insert(std::make_pair(taskid, std::make_pair(RT_GetUinRoom, genid)));
#endif
	
}

void RoomClient::respGetUinRoom(int result, jsonxx::Object* jsonobj, std::string genid)
{
	LOG_INFO("respGetUinRoom %d", result);

	using namespace jsonxx;

	bool succeed = false;

	if (RoomErr_NoErr == result)  //在本服
	{
		if (jsonobj != NULL && jsonobj->has<jsonxx::Object>("room_info"))
		{
			const jsonxx::Object& roomnode = jsonobj->get<jsonxx::Object>("room_info");

			RoomDesc rm;
			parseRoomDesc(roomnode, rm);
			
			succeed = true;

			rm.from_http = true;

			m_searchUinResults.push_back(std::make_pair(0, rm));
		}
		else
			result = RoomErr_MissingParams;
	}

	if (!succeed)
	{
		m_searchUinResults.push_back(std::make_pair(result, RoomDesc()));
	}

	reqSearchNextUin(genid);
}

void RoomClient::reqManorRoom(int destUin)
{
	std::string url =
		RoomReqBuilder(getRoomServerUrl(getRoomServerDefault()), "/server/room")
		.addParam("cmd", "query_role_manor_room")
		.addParam("des_uin", destUin)
		.addParam("src_uin", GetClientInfoProxy()->getUin())
		.end();

	LOG_INFO("  url = '%s'", url.c_str());

#if ROOM_USE_NEW_HTTPMANAGER
	UInt32 taskid = ++s_TaskID;
	ROOM_TASK_DATA* task_data = ENG_NEW_LABEL(ROOM_TASK_DATA, kMemGame) (taskid, RT_SearchManorUinRoom, "", this);
	
	m_httptask_array[taskid] = Http::GetHttpManager().Request(url, "", task_data, 0, nullptr, HandleRequestEnd);

	//s_RoomTaskArray[this].emplace_back(task_data);
#else
	int taskid = HttpDownloadMgr::GetInstance().rpc(url);

	m_httpRequests.insert(std::make_pair(taskid, std::make_pair(RT_SearchManorUinRoom, "")));
#endif

	m_lastPingSendTime = Rainbow::Timer::getSystemTick();
}

void RoomClient::respManorRoom(int result, jsonxx::Object* jsonobj)
{
	using namespace jsonxx;
	RoomDesc rm;
	if (RoomErr_NoErr == result)  //在本服
	{
		m_lastPing = Rainbow::Timer::getSystemTick() - m_lastPingSendTime;

		if (jsonobj != NULL && jsonobj->has<jsonxx::Object>("room_info"))
		{
			const jsonxx::Object& roomnode = jsonobj->get<jsonxx::Object>("room_info");
			parseRoomDesc(roomnode, rm);
			rm.from_http = true;
			m_searchUinResults.push_back(std::make_pair(0, rm));



			LOG_INFO("respManorRoom get roomdesc");
		}
		else
			result = RoomErr_MissingParams;

		MNSandbox::GetGlobalEvent().Emit<int, RoomDesc>("RoomManager_onGetManorRoom", result, rm);
	}
	else if (RoomErr_other_room == result) //在外服
	{
		m_lastPing = Rainbow::Timer::getSystemTick() - m_lastPingSendTime;			

		if (jsonobj != NULL && jsonobj->has<Number>("uin") && jsonobj->has<Number>("port") && 
			jsonobj->has<MINIW::String>("ip") &&
			jsonobj->has<Number>("port") &&
			jsonobj->has<MINIW::String>("p_ip") &&
			jsonobj->has<Number>("p_port") &&
			jsonobj->has<MINIW::String>("pr_ip") &&
			jsonobj->has<Number>("pr_port"))
		{
			GameNetCfg uinNetCfg;
			uinNetCfg.roomhosthttp_port = (int)jsonobj->get<Number>("port");
			uinNetCfg.roomhosthttp_url = jsonobj->get<MINIW::String>("ip");
			uinNetCfg.roomhosthttp_port = (int)jsonobj->get<Number>("port");
			uinNetCfg.natpunch_url = jsonobj->get<MINIW::String>("p_ip");
			uinNetCfg.natpunch_port = (int)jsonobj->get<Number>("p_port");
			uinNetCfg.proxy_url = jsonobj->get<MINIW::String>("pr_ip");
			uinNetCfg.proxy_port = (int)jsonobj->get<Number>("pr_port");

			int uin = (int)jsonobj->get<Number>("uin");
			m_uinRoomServerCfg[uin] = uinNetCfg;

			reqGetManorUinRoom(uin, uinNetCfg);
		}
		else
		{
			LOG_INFO("respManorRoom: failed, json missing params");
			MNSandbox::GetGlobalEvent().Emit<int, RoomDesc>("RoomManager_onGetManorRoom", result, rm);
		}
	}
	else
	{
		MNSandbox::GetGlobalEvent().Emit<int, RoomDesc>("RoomManager_onGetManorRoom", result, rm);
	}
}

void RoomClient::reqHotRoomList(int gamelabel, int flushpos, int host_type/* =0*/)
{
	std::string url = "";
	//海外请求增加语言参数 lang
	url = RoomReqBuilder(getRoomServerUrl(getRoomServerDefault()), "/server/room")
		.addParam("apiid", GetClientInfoProxy()->GetAppId())
		.addParam("cmd", "query_hot_map")
		.addParam("flush_pos", flushpos)
		.addParam("game_label", gamelabel)
		.addParam("host_type", host_type)
		.addParam("lang", GetIWorldConfigProxy()->getGameData("lang"))
		.end();

	LOG_INFO(" reqHotRoomList:gamelabel= %d url = '%s'", gamelabel,url.c_str());

#if ROOM_USE_NEW_HTTPMANAGER
	UInt32 taskid = ++s_TaskID;
	ROOM_TASK_DATA* task_data = ENG_NEW_LABEL(ROOM_TASK_DATA, kMemGame) (taskid, RT_HotList, "", this);
	
	m_httptask_array[taskid] = Http::GetHttpManager().Request(url, "", task_data, 0, nullptr, HandleRequestEnd);

	//s_RoomTaskArray[this].emplace_back(task_data);
#else
	int taskid = HttpDownloadMgr::GetInstance().rpc(url);

	m_httpRequests.insert(std::make_pair(taskid, std::make_pair(RT_HotList, "")));
#endif

}

void RoomClient::respHotRoomList(int result, jsonxx::Object* jsonobj)
{
	LOG_INFO("respHotRoomList %d", result);

	using namespace jsonxx;

	if (RoomErr_NoErr == result)
	{
		std::vector<HotRoomDesc> rooms;
		if (jsonobj != NULL && jsonobj->has<jsonxx::Array>("hot_map"))
		{
			const jsonxx::Array& listnode = jsonobj->get<jsonxx::Array>("hot_map");

			for (int i = 0; i < (int)listnode.size(); i++)
			{
				const jsonxx::Object& src = listnode.get<jsonxx::Object>(i);

				HotRoomDesc hotrm;
				parseHotRoomDesc(src, hotrm);

				rooms.push_back(hotrm);
			}
		}

		int flushPos = 0;
		if (jsonobj != NULL && jsonobj->has<jsonxx::Number>("flush_pos"))
			flushPos = (int)jsonobj->get<jsonxx::Number>("flush_pos");

		MNSandbox::GetGlobalEvent().Emit<int, HotRoomDesc*, int, int>("RoomManager_onGetHotRoomHttp", result, rooms.data(), rooms.size(), flushPos);
	}
	else
	{
		MNSandbox::GetGlobalEvent().Emit<int, HotRoomDesc*, int, int>("RoomManager_onGetHotRoomHttp", result, NULL, 0,0);
	}
}


bool RoomClient::reqCreateRoom(const ROOMINFO &room, int headicon, bool guestmode, std::string fromowidstr, std::string game_session_id /*= ""*/)
{
	if (GetGameNetManagerPtr() == NULL && 
		(!GetGameNetManagerPtr()->isHost() ||
		 !GetGameNetManagerPtr()->getConnection()))
	{
		return false;
	}

	LOG_INFO("@ createRoom [%s]", fromowidstr.c_str() );

#ifdef IWORLD_SERVER_BUILD
	//租赁服未配置roomserverip
	if (GetGameInfoProxy()->GetRoomHostType() == ROOM_SERVER_RENT)
	{
		std::string thumbnail = (m_lastUploadThumbOwid == room.OWID) ? m_lastUploadThumbUrl : "";
		MINIW::ScriptVM::game()->callFunction("rentLuaEvent", "ss", "set_thumbnail", thumbnail.c_str());	  //上报截图url
		MINIW::ScriptVM::game()->callFunction("rentLuaEvent", "ss", "room_info", "");                      //node上报gs信息到server
		if (!m_hasRoomServerIp)
		{
			WarningStringMsg("[server]reqCreateRoom start hearbeat");
			reqHeartbeatRent(GetClientInfoProxy()->getUin());  //心跳开始
			return true;
		}
	}
#endif


	char owidstr[128];
	std::sprintf(owidstr, "%lld", room.OWID);

	char mapName[128] = {};
	if (GetClientInfoProxy()->getIsOverseasVer())
		MINIW::ScriptVM::game()->callFunction("GetMapNameForCreateRoom", "s>s", owidstr, mapName);

	std::string passwd_md5 = (room.Password[0] == '\0') ? "" : gFunc_getmd5(room.Password);

	std::string thumbnail = (m_lastUploadThumbOwid == room.OWID) ? m_lastUploadThumbUrl : "";

	int curtimestamp = (int)(MINIW::GetTimeStamp());
	MINIW::ScriptVM::game()->callFunction("getServerTime", ">i", &curtimestamp);
	int uin = GetClientInfoProxy()->getUin();
	int headFrameId = GetClientInfoProxy()->getHeadFrameId();
	int apiid = GetClientInfoProxy()->GetAppId();

	int frameValue = 0;
	MINIW::ScriptVM::game()->callFunction("MainV2Config_isUseNewMultiLobby", ">i", &frameValue);

	int hasAvatar = 0;
	const char *customSkin = GetClientInfoProxy()->getRoleCustomSkin();
	if (customSkin != NULL && customSkin != "") {
		hasAvatar = 1;
	}

	std::string s2, s2t;
	GetDefManagerProxy()->getS2(s2, s2t);
	char tokenBuf[128];
	std::sprintf(tokenBuf, "%d%s%d", curtimestamp, s2.c_str(), room.OwnerUin);
	std::string token = gFunc_getmd5(tokenBuf);

	ENG_DELETE(m_ingameRoomServerCfg);
	m_ingameRoomServerCfg = ENG_NEW(GameNetCfg)();
	(*m_ingameRoomServerCfg) = (*getRoomServerDefault());

	bool isCollaborationMode = false;
	//fix 房间内单机转联机时，需要传递地图id去判断是否是好友联机
	MINIW::ScriptVM::game()->callFunction("IsArchiveMapCollaborationMode", "w>b", atoll(fromowidstr.c_str()), &isCollaborationMode);
	int iConnectMode = isCollaborationMode ? 1 : 0;

	// ********: 联机组队需求增加相关接口组队模式的传参  codeby:huangrulin
	if (2 == room.ConnectMode)
		iConnectMode = 2;
	else if (room.ConnectMode > 0)
		iConnectMode = room.ConnectMode;

	mCurrentRoom.ConnectMode = iConnectMode;
	mCurrentRoom.PublicType = room.PublicType;
	if (mCurrentRoom.PreiNameIdx >= 0)
		memset(mCurrentRoom.CustomRoomName, 0, sizeof(mCurrentRoom.CustomRoomName));

	m_curGameSessionId = game_session_id.empty() ? GetIWorldConfigProxy()->getGameSessionId().c_str() : game_session_id;

	int iProxyConnected = 0;
	UDPConnection* pConn = GameNetManager::getInstance()->getConnection();
	if (pConn && pConn->isHostReady())
		iProxyConnected = 1;

	RoomReqBuilder urlBuilder(getRoomServerUrl(getRoomServerDefault()), "/server/room");
	urlBuilder.addParam("can_trace", room.CanTrace);	//是否可以被追踪
	urlBuilder.addParam("cmd", "create_room");  //string 命令类型
	urlBuilder.addParam("connect_mode", iConnectMode); //int 1-协作模式 0-非协作模式 2组队模式
	urlBuilder.addParam("country", gFunc_getCountry());	//国家类型
	urlBuilder.addParam("desc", room.Description, true);  //string 房间描述
	urlBuilder.addParam("device", room.ApiID);  //int apiid 设备类型(手机/电脑) -> 渠道号
	urlBuilder.addParam("extra_data", room.ExtraData, true);
	if (GetClientInfoProxy()->getIsOverseasVer())
	{
		//海外这个字段应该受大厅配置控制，frame的值会影响query_room_list接口
		int frameValue = 0;
		MINIW::ScriptVM::game()->callFunction("MainV2Config_isUseNewMultiLobby", ">i", &frameValue);//海外独有 
		urlBuilder.addParam("frame", frameValue); //服务端做数据隔离
	}
	urlBuilder.addParam("game_label", room.GameLabel);  //int 房间类型 -> GameLabel
	urlBuilder.addParam("has_avatar", hasAvatar); //int 用户是否有avatar头像
#ifdef IWORLD_SERVER_BUILD
	urlBuilder.addParam("host_type", 1);
#endif
	urlBuilder.addParam("map_id", gFunc_getmd5(owidstr));  //string 地图编号(MD5加密)
	if (GetClientInfoProxy()->getIsOverseasVer())
	{
		//海外独有 
		urlBuilder.addParam("map_name", mapName, true);		//地图名称
	}
	urlBuilder.addParam("map_type", fromowidstr);  //string from地图编号
	urlBuilder.addParam("map_version", room.MapVersion);	// 地图版本号
	urlBuilder.addParam("max_count", room.MaxPlayerCount);  //int 人数上限
	urlBuilder.addParam("net_area", room.Province);  //int IP区域
	urlBuilder.addParam("net_isp", room.Isp);  //int 网络运营商(中国电信/中国移动/中国联通)
	urlBuilder.addParam("net_status", 2);  //int 网络状态 1 - 优秀 2 - 良好 3 - 一般  //TODO
	urlBuilder.addParam("passwd", passwd_md5);  //string 进入密码(MD5加密)
	if (GetClientInfoProxy()->getIsOverseasVer())
	{
		//海外独有 
		urlBuilder.addParam("proxy_connected", iProxyConnected);
	}
	urlBuilder.addParam("proxy_ip", mNetCfg.proxy_url);		// 转发服地址
	urlBuilder.addParam("proxy_port", mNetCfg.proxy_port);	// 转发服端口
	urlBuilder.addParam("punch_ip", mNetCfg.natpunch_url);	// 打洞服地址
	urlBuilder.addParam("punch_port", mNetCfg.natpunch_port);	// 打洞服端口
	urlBuilder.addParam("right", (guestmode ? 2 : 1)); //int 进入权限 1 - 可破坏 2 - 不可破坏
	urlBuilder.addParam("room_name", room.RoomTitle, true); //string 房间名字
	urlBuilder.addParam("room_type", room.GameType);  //int 房间类型 -> OWType
	urlBuilder.addParam("s2t", s2t.c_str());  //string 账号服时间戳
#ifdef IWORLD_SERVER_BUILD
	// urlBuilder.addParam("server_ip", MiniWorldPreferences::getInstance()->getEnterHashValue("ip").c_str(), true);
	// urlBuilder.addParam("server_port", atoi(MiniWorldPreferences::getInstance()->getEnterHashValue("port").c_str()));
	urlBuilder.addParam("server_ip", GetClientInfoProxy()->getEnterParam("ip"), true);
	urlBuilder.addParam("server_port", GetClientInfoProxy()->getEnterParam("port"));
#endif
	urlBuilder.addParam("thumbnail", thumbnail, true);  //string 缩略图
	urlBuilder.addParam("time", curtimestamp); //时间戳
	urlBuilder.addParam("token", token.c_str()); //加密验证串
	urlBuilder.addParam("uicon", headicon); //int 用户头像
	urlBuilder.addParam("uicon_box", headFrameId); //int 用户头像框id
	urlBuilder.addParam("uin", room.OwnerUin); //int 用户迷你号
	urlBuilder.addParam("uname", room.OwnerName, true); //string 用户名字
	urlBuilder.addParam("use_proxy", (isProxyOnly(room.OwnerUin) ? 1 : 0));	//int 是否使用转发
	urlBuilder.addParam("version", GetClientInfoProxy()->GetClientVersionStr());	// 版本号
	urlBuilder.calcAuth();
#ifndef IWORLD_SERVER_BUILD
	urlBuilder.addParam("public_type", mCurrentRoom.PublicType); //0 公开 1 私密
	urlBuilder.addParam("prei_room_name_idx", mCurrentRoom.PreiNameIdx); /*  预设房间名序号 >0代表选择了预设房间名 */

	if (mCurrentRoom.CustomRoomName[0] != 0)
	{	
		//如果传空字符串，服务器会把这个当成有效值，对room_name进行覆盖,导致地图卡不显示房间名
		urlBuilder.addParam("custom_room_name", mCurrentRoom.CustomRoomName);
	}
	
	//服务器埋点参数，被注释的是上面已经有了
	//urlBuilder.addParam("uin", room.OwnerUin);  //int 用户迷你号
	urlBuilder.addParam("regapiid", this->GetRegApiid());
	urlBuilder.addParam("cltapiid", GetClientInfoProxy()->GetAppId());
	urlBuilder.addParam("cltversion", GetClientInfoProxy()->clientVersion());
	//urlBuilder.addParam("country", gFunc_getCountry());	//国家类型
	urlBuilder.addParam("lang", GetClientInfoProxy()->getGameData("lang"));
	urlBuilder.addParam("game_session_id", m_curGameSessionId);
	urlBuilder.addParam("session_id", GetIWorldConfigProxy()->getSession_id());
	urlBuilder.addParam("room_token", GetRoomInfoToken());
#endif

	std::string url = urlBuilder.end();
	if (GetClientInfoProxy()->getIsOverseasVer())
	{
		//海外创建房间添加创建路径类型，不参与签名
		char tmp[64] = { 0 };
		std::sprintf(tmp, "&create_mode=%d", GetClientInfoProxy()->GetCreateRoomPathType());
		url.append(tmp);
	}

	LOG_INFO("  url = '%s'", url.c_str());

#if ROOM_USE_NEW_HTTPMANAGER
	UInt32 taskid = ++s_TaskID;
	ROOM_TASK_DATA* task_data = ENG_NEW_LABEL(ROOM_TASK_DATA, kMemGame) (taskid, RT_CreateRoom, "", this);

	m_httptask_array[taskid] = Http::GetHttpManager().Request(url, "", task_data, 0, nullptr, HandleRequestEnd);

	//s_RoomTaskArray[this].emplace_back(task_data);
#else
	int taskid = HttpDownloadMgr::GetInstance().rpc(url, "", 0, true);

	m_httpRequests.insert(std::make_pair(taskid, std::make_pair(RT_CreateRoom, "")));
#endif
	
	m_lastPingSendTime = Rainbow::Timer::getSystemTick();

	updateNearbyLanRoom(room);

	return true;
}

void RoomClient::respCreateRoom(int result, jsonxx::Object* jsonobj, std::string genid)
{
	LOG_INFO("respCreateRoom %d", result);

	if (RoomErr_NoErr == result)
	{
		m_lastPing = Rainbow::Timer::getSystemTick() - m_lastPingSendTime;

		if (jsonobj != NULL && jsonobj->has<jsonxx::Number>(CustomFlagArg))
		{
			this->updateCustomRoomNameFlag((int)jsonobj->get<jsonxx::Number>(CustomFlagArg));
		}

		// 由主机发送心跳消息
		reqHostUpdateRoom(GetClientInfoProxy()->getUin(), m_lastStage, true);
#ifndef IWORLD_SERVER_BUILD		
		GetGameInfoProxy()->SetRoomHostType(ROOM_BY_PLAYER);
#endif
	}
	else
	{
		// 创建联机房间失败
		if (!m_bReCreatRoom)
		{
			int ext_info = 0;
			if (jsonobj != NULL && jsonobj->has<jsonxx::Number>("ext_info"))
			{
				ext_info = (int)jsonobj->get<jsonxx::Number>("ext_info");
			}
			//ge GetGameEventQue().postRSConnect(result, genid, ext_info);
			MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
				SetData_Number("result", result).
				SetData_String("genid", genid).
				SetData_Number("detailreason", ext_info);
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GIE_RSCONNECT_RESULT", sandboxContext);
			}
		}
	}
	m_bReCreatRoom = false;
}

// ********: 联机组队需求增加相关接口组队模式的传参  codeby:huangrulin
bool RoomClient::reqJoinRoom(int myUin, int roomOwnerUin, const char* passwd, std::string genid, int CanTrace/*=0*/, int connectMode /*= 0*/)
{
	LOG_INFO("@ reqJoinRoom");

	// 添加进房间开始埋点
#ifndef IWORLD_SERVER_BUILD
	if (g_pGameAnalytics) {
		std::map<std::string, GameAnalytics::Value> eventData = {
			{"player_uin", GameAnalytics::Value(myUin)},
			{"room_owner_uin", GameAnalytics::Value(roomOwnerUin)},
			{"has_password", GameAnalytics::Value(passwd != NULL && passwd[0] != '\0')},
			{"can_trace", GameAnalytics::Value(CanTrace)},
			{"connect_mode", GameAnalytics::Value(connectMode)},
			{"genid", GameAnalytics::Value(genid)}
		};
		GameAnalytics::TrackEvent("player_request_join_room", eventData);
	}
#endif

	std::string passwd_md5 = (passwd == NULL || passwd[0] == '\0') ? "" : gFunc_getmd5(passwd);

	//room owner is in another roomserver
	GameNetCfg* roomserver = getRoomServerByUin(roomOwnerUin);
	if (roomserver == NULL)
		roomserver = getRoomServerDefault();

	m_ingameRoomServerCfg = ENG_NEW(GameNetCfg)();
	(*m_ingameRoomServerCfg) = (*roomserver);

	std::string httpServer = gethttpServer(roomOwnerUin);
	m_nCurrentRoomOwner = roomOwnerUin;
	
	std::string s2, s2t;
	GetDefManagerProxy()->getS2(s2, s2t);
	char tokenBuf[128];
	int curtimestamp = (int)(MINIW::GetTimeStamp());
	MINIW::ScriptVM::game()->callFunction("getServerTime", ">i", &curtimestamp);
	std::sprintf(tokenBuf, "%d%s%d", curtimestamp, s2.c_str(), myUin);
	std::string token = gFunc_getmd5(tokenBuf);

	int iConnectMode = GetClientInfoProxy()->IsArchiveMapCollaborationMode() ? 1 : 0;
	// ********: 联机组队需求增加相关接口组队模式的传参  codeby:huangrulin
	if (connectMode > 0)
		iConnectMode = connectMode;
	mCurrentRoom.ConnectMode = iConnectMode;

#ifdef IWORLD_UNIVERSE_BUILD
	GetIWorldConfigProxy()->generateGameSessionSeed();
#else
	m_curGameSessionId = GetIWorldConfigProxy()->getGameSessionId();
	if (m_curGameSessionId.empty())
	{
		m_curGameSessionId = GetIWorldConfigProxy()->generateGameSessionId();
	}
#endif // IWORLD_UNIVERSE_BUILD

	
	//重设roomExtraData  
	mCurrentRoom.ExtraData[0] = 0;
	RoomDesc* hostRoom = nullptr;
	MNSandbox::GetGlobalEvent().Emit<RoomDesc*&, int>("RoomManager_findRoom", hostRoom, roomOwnerUin);
	if (hostRoom)
	{
		int maxdst = hostRoom->extraData.length() + 1;
		if (sizeof(mCurrentRoom.ExtraData) < maxdst)
			maxdst = sizeof(mCurrentRoom.ExtraData);

		MyStringCpy(mCurrentRoom.ExtraData, maxdst, hostRoom->extraData.c_str());
	}
#ifdef IWORLD_UNIVERSE_BUILD
	if (hostRoom)
	{
		m_curGameSessionId = GetIWorldConfigProxy()->generateGameSessionId(hostRoom->map_type);
	}
	else
	{
		m_curGameSessionId = GetIWorldConfigProxy()->generateGameSessionId();
	}
#endif
	std::string url = 
		RoomReqBuilder(httpServer, "/server/room")
		.addParam("can_trace", CanTrace)
		.addParam("cmd", "enter_room")
		.addParam("connect_mode", mCurrentRoom.ConnectMode)  //0-普通联机, 1-协作模式联机 2-组队模式联机 3-私人房间
		.addParam("owner_uin", roomOwnerUin)
		.addParam("passwd", passwd_md5)
		.addParam("s2t", s2t.c_str())
		.addParam("time", curtimestamp)
		.addParam("token", token.c_str())
		.addParam("uin", myUin)
		.calcAuth()
		//服务器埋点参数，被注释的是上面已经有了
		//.addParam("uin", myUin)  //int 用户迷你号
		.addParam("regapiid", this->GetRegApiid())
		.addParam("cltapiid", GetClientInfoProxy()->GetAppId())
		.addParam("cltversion", GetClientInfoProxy()->clientVersion())
		.addParam("country", gFunc_getCountry())	//国家类型
		.addParam("lang", GetClientInfoProxy()->getGameData("lang"))
		.addParam("game_session_id", m_curGameSessionId)
		.addParam("session_id", GetIWorldConfigProxy()->getSession_id())
		.addParam("room_token", GetRoomInfoToken())
		.addParam("scene_id", GetEnterRoomSceneID())
		.addParam("network_type", mNetCfg.network_type)
		.end();

	LOG_INFO("  url = '%s'", url.c_str());

#if ROOM_USE_NEW_HTTPMANAGER
	UInt32 taskid = ++s_TaskID;

	ROOM_TASK_DATA* task_data = ENG_NEW_LABEL(ROOM_TASK_DATA, kMemGame) (taskid, RT_JoinRoom, genid, this);
	
	m_httptask_array[taskid] = Http::GetHttpManager().Request(url, "", task_data, 0, nullptr, HandleRequestEnd);

	//s_RoomTaskArray[this].emplace_back(task_data);
#else
	int taskid = HttpDownloadMgr::GetInstance().rpc(url, "", 0, true);

	m_httpRequests.insert(std::make_pair(taskid, std::make_pair(RT_JoinRoom, genid)));
#endif
	
	MINIW::ScriptVM::game()->callFunction("NativeSetJoinRptSessionID", "s", m_curGameSessionId.c_str());
	return true;
}

void RoomClient::respJoinRoom(int result, jsonxx::Object* jsonobj, std::string genid)
{
	LOG_INFO("respJoinRoom %d", result);

	using namespace jsonxx;
	if (RoomErr_NoErr == result)
	{
		m_HeartbeatTicks = 0;
		m_LastHeartbeatReturn = 0;

		if (jsonobj != NULL && jsonobj->has<MINIW::String>("punch_ip") && jsonobj->has<Number>("punch_port"))
		{
			std::string punch_url = jsonobj->get<MINIW::String>("punch_ip");
			unsigned short punch_port = (unsigned short)jsonobj->get<Number>("punch_port");
			
			if(GetGameNetManagerPtr()->getConnection() != NULL)
			{ //异步链接是不是被delete了？
				GetGameNetManagerPtr()->getConnection()->setPunchIPPort(punch_url, punch_port);
			}
		}

		if (jsonobj != NULL && jsonobj->has<MINIW::String>("proxy_ip") && jsonobj->has<Number>("proxy_port"))
		{
			std::string proxy_url = jsonobj->get<MINIW::String>("proxy_ip");
			unsigned short proxy_port = (unsigned short)jsonobj->get<Number>("proxy_port");
			
			if(GetGameNetManagerPtr()->getConnection() != NULL){
				GetGameNetManagerPtr()->getConnection()->setProxyIPPort(proxy_url, proxy_port);
			}
		}
	}
	//else if (result == 15 || result == 21) {            //租赁服测试使用
	//	m_HeartbeatTicks = 0;
	//	m_LastHeartbeatReturn = 0;
	//	result = 0;
	//}
	else
	{
		m_HeartbeatTicks = -1;
		m_LastHeartbeatReturn = -1;
	}

	MNSandbox::GetGlobalEvent().Emit<int, std::string>("RoomManager_onRespJoinRoomHttp", result, genid);

	// 添加进房间响应埋点
#ifndef IWORLD_SERVER_BUILD
	if (g_pGameAnalytics) {
		std::map<std::string, GameAnalytics::Value> eventData = {
			{"result_code", GameAnalytics::Value(result)},
			{"genid", GameAnalytics::Value(genid)},
			{"player_uin", GameAnalytics::Value(GetClientInfoProxy()->getUin())},
			{"room_owner", GameAnalytics::Value(m_nCurrentRoomOwner)}
		};
		
		if (RoomErr_NoErr == result) {
			GameAnalytics::TrackEvent("player_join_room_success", eventData);
		} else {
			eventData["error_code"] = GameAnalytics::Value(result);
			GameAnalytics::TrackEvent("player_join_room_failed", eventData);
		}
	}
#endif
}

bool RoomClient::reqLeaveRoom(int myUin, int roomOwner, int cause)
{
	LOG_INFO("@ reqLeaveRoom");
	if (m_ingameRoomServerCfg == NULL)
		return true;

	m_HeartbeatTicks = -1;
	m_LastHeartbeatReturn = -1;

	std::string httpServer = gethttpServer(roomOwner);
	std::string url =
		RoomReqBuilder(httpServer, "/server/room")
		.addParam("cmd", "leave_room")
		.addParam("uin", myUin)
		.calcAuth()
		//服务器埋点参数，被注释的是上面已经有了
		//.addParam("uin", myUin)  //int 用户迷你号
		.addParam("regapiid", this->GetRegApiid())
		.addParam("cltapiid", GetClientInfoProxy()->getApiId())
		.addParam("cltversion", GetClientInfoProxy()->clientVersion())
		.addParam("country", gFunc_getCountry())	//国家类型
		.addParam("lang", GetClientInfoProxy()->getGameData("lang"))
		.addParam("game_session_id", m_curGameSessionId)
		.addParam("session_id", GetIWorldConfigProxy()->getSession_id())
		.addParam("room_token", GetRoomInfoToken())
		.addParam("leave_cause", cause)
		.end();

	LOG_INFO("  url = '%s'", url.c_str());

#if ROOM_USE_NEW_HTTPMANAGER
	UInt32 taskid = ++s_TaskID;
	ROOM_TASK_DATA* task_data = ENG_NEW_LABEL(ROOM_TASK_DATA, kMemGame) (taskid, RT_LeaveRoom, "", this);
	
	m_httptask_array[taskid] = Http::GetHttpManager().Request(url, "", task_data, 0, nullptr, HandleRequestEnd);

	//s_RoomTaskArray[this].emplace_back(task_data);
#else
	int taskid = HttpDownloadMgr::GetInstance().rpc(url, "", 0, true);

	m_httpRequests.insert(std::make_pair(taskid, std::make_pair(RT_LeaveRoom, "")));
#endif
	
	return true;
}

void RoomClient::respLeaveRoom(int result, jsonxx::Object* jsonobj)
{
	LOG_INFO("respLeaveRoom %d", result);

	ENG_DELETE(m_ingameRoomServerCfg);
}

bool RoomClient::reqCloseRoom(int myUin)
{
	LOG_INFO("@ reqCloseRoom");

	m_HeartbeatTicks = -1;
	m_HeartbeatRentStartTime = 0;
	m_HeartbeatTicksRent = 0;
	m_LastHeartbeatReturn = -1;

	//Fix 【【联机】普通联机房主关闭房间时，发送的close_room请求前面计算方式不对】
	/*https://www.tapd.cn/22897851/bugtrace/bugs/view?bug_id=1122897851001047068*/
	//20220402 huangrulin
	std::string url =
		RoomReqBuilder(getRoomServerUrl(m_ingameRoomServerCfg, 1), "/server/room")
		.addParam("cmd", "close_room")
		.addParam("uin", myUin)
		.calcAuth()
		.addParam("apiid", GetClientInfoProxy()->GetAppId())
		.addParam("country", gFunc_getCountry())
		.addParam("lang", GetClientInfoProxy()->getGameData("lang"))
		.addParam("ver", GetClientInfoProxy()->clientVersionStr())
		//服务器埋点参数，被注释的是上面已经有了
		//.addParam("uin", myUin)  //int 用户迷你号
		.addParam("regapiid", this->GetRegApiid())
		.addParam("cltapiid", GetClientInfoProxy()->getApiId())
		.addParam("cltversion", GetClientInfoProxy()->clientVersion())
		//.addParam("country", gFunc_getCountry())	//国家类型
		//.addParam("lang", GetClientInfoProxy()->getGameData("lang"))
		.addParam("game_session_id", m_curGameSessionId)
		.addParam("session_id", GetIWorldConfigProxy()->getSession_id())
		.addParam("room_token", GetRoomInfoToken())
		.end();

	LOG_INFO("  url = '%s'", url.c_str());

#if ROOM_USE_NEW_HTTPMANAGER
	UInt32 taskid = ++s_TaskID;
	ROOM_TASK_DATA* task_data = ENG_NEW_LABEL(ROOM_TASK_DATA, kMemGame) (taskid, RT_CloseRoom, "", this);
	
	m_httptask_array[taskid] = Http::GetHttpManager().Request(url, "", task_data, 0, nullptr, HandleRequestEnd);

	//s_RoomTaskArray[this].emplace_back(task_data);
#else
	int taskid = HttpDownloadMgr::GetInstance().rpc(url, "", 0, true);

	m_httpRequests.insert(std::make_pair(taskid, std::make_pair(RT_CloseRoom, "")));
#endif
	m_isHost = false;

	disconnect();

	return true;
}

void RoomClient::respCloseRoom(int result, jsonxx::Object* jsonobj)
{
	LOG_INFO("respCloseRoom %d", result);

	ENG_DELETE(m_ingameRoomServerCfg);
}

void RoomClient::doUpdateHeartbeatHttp()
{
	if (m_isHost)
	{
		// 主机发送心跳消息
		reqHostUpdateRoom(GetClientInfoProxy()->getUin(), m_lastStage, true);
	}else{
		// 客机发送心跳消息
		reqHeartbeat(GetClientInfoProxy()->getUin());
	}
}

//void RoomClient::RemoveTaskData(ROOM_TASK_DATA* task_data)
//{
//	s_RoomTaskArray[this].removeSlow(task_data);
//}

void RoomClient::addDeleteRoomClient(RoomClient*roomClient) {
	m_deleteRoomClients.push_back(roomClient);

}

void RoomClient::deleteRoomClient() {
	for (auto iter = m_deleteRoomClients.begin(); iter != m_deleteRoomClients.end(); iter++)
	{
		ENG_DELETE(*iter);
	}
	m_deleteRoomClients.clear();
}

// 针对现网崩溃加的有效性检查 code_by:huangfubin 2022.10.24
bool RoomClient::isValidRoomClient(RoomClient* rc)
{
	if (!rc)
	{
		return false;
	}
	auto iter = g_room_client_map.find(rc);
	if (iter != g_room_client_map.end() && iter->second == rc->m_UniqueId)
	{
		return true;
	}
	LOG_WARNING("RoomClient is invalid");
	assert(false);
	return false;
}

void RoomClient::uin2raknetguid(int uin, RakNet::RakNetGUID& guid)
{
	// 等后续版本再开启
#ifdef GUID_PROTECT
	uint64_t protect = uin / 7 + 12345;
	uint64_t ret = (protect << 32) | ((uint64_t)uin ^ protect);
	guid.g = ret;
#else

	char szUin[32];

	snprintf(szUin, sizeof(szUin), "%d", uin);
	guid.FromString(szUin);
#endif
}

RakNet::SystemAddress RoomClient::getHostSystemAddr(RakNet::RakPeerInterface* host)
{
	RakNet::SystemAddress ret = UNASSIGNED_SYSTEM_ADDRESS;
	if (!GetGameNetManagerPtr())
		return ret;
	int uin = 0;
	if (GetGameNetManagerPtr()->isServerRoom())
		uin = RENT_HOST_UIN;
	else
		uin = GetGameNetManagerPtr()->getHostUin();
	
	RakNet::RakNetGUID guid;
	uin2raknetguid(uin, guid);
	ret = host->GetSystemAddressFromGuid(guid);
	if (ret == UNASSIGNED_SYSTEM_ADDRESS && GetGameNetManagerPtr()->getConnection())
		ret = GetGameNetManagerPtr()->getConnection()->getProxyServerAddr();

	return ret;
}

void RoomClient::reportRaknetInfo()
{
	if (m_isHost)
		return;
	if (!GetGameNetManagerPtr())
		return;
	if (!GetGameNetManagerPtr()->getConnection())
		return;

	if (!m_NsVersionRaknetInfoReportConfig.init)
	{
		m_NsVersionRaknetInfoReportConfig.init = true;
		char luaData[4096] = "";
		MINIW::ScriptVM::game()->callFunction("GetNsVersionRaknetInfoReport", ">s", luaData);
		jsonxx::Value jsonobj;
		if (jsonobj.parse(luaData) && jsonobj.is<jsonxx::Object>())
		{
			jsonxx::Object contentObj = jsonobj.get<jsonxx::Object>();
			if (contentObj.has<jsonxx::Number>("open"))
			{
				m_NsVersionRaknetInfoReportConfig.open = contentObj.get<jsonxx::Number>("open");
			}
			if (contentObj.has<jsonxx::Number>("open_all"))
			{
				m_NsVersionRaknetInfoReportConfig.open_all = contentObj.get<jsonxx::Number>("open_all");
			}
			if (contentObj.has<jsonxx::Number>("interval"))
			{
				m_NsVersionRaknetInfoReportConfig.interval = contentObj.get<jsonxx::Number>("interval");
			}
			if (contentObj.has<jsonxx::Number>("max_ping"))
			{
				m_NsVersionRaknetInfoReportConfig.max_ping = contentObj.get<jsonxx::Number>("max_ping");
			}
			if (contentObj.has<jsonxx::Number>("max_lost_rate"))
			{
				m_NsVersionRaknetInfoReportConfig.max_lost_rate = contentObj.get<jsonxx::Number>("max_lost_rate");
			}
			if (contentObj.has<jsonxx::Array>("limit_wids"))
			{
				m_NsVersionRaknetInfoReportConfig.limit_wids.clear();
				jsonxx::Array limit_wids = contentObj.get<jsonxx::Array>("limit_wids");
				for (size_t i = 0; i < limit_wids.size(); i++)
				{
					uint64_t wid = limit_wids.get<jsonxx::Number>(i);
					m_NsVersionRaknetInfoReportConfig.limit_wids.push_back(wid);
				}
			}
		}
		if (m_NsVersionRaknetInfoReportConfig.interval <= 0)
			m_NsVersionRaknetInfoReportConfig.interval = 60;
	}

	if (m_NsVersionRaknetInfoReportConfig.open != 1)
		return;

	RakPeerInterface* peer = (RakPeerInterface*)GetGameNetManagerPtr()->getConnection()->GetRakPeerInterface();
	if (!peer)
		return;
	SystemAddress hostUin = getHostSystemAddr(peer);
	if (hostUin == UNASSIGNED_SYSTEM_ADDRESS)
		return;

	++m_HeartbeatTicksReport;
	//if (m_HeartbeatTicksReport % (20 * 10) == 0)
	//{
	//	peer->Ping(hostUin);
	//}	
	if (m_HeartbeatTicksReport % (20 * m_NsVersionRaknetInfoReportConfig.interval) == 0)
	{
		if (m_NsVersionRaknetInfoReportConfig.open_all != 1)
		{
			int lastPing = GetGameNetManagerPtr()->getClientLastPing();
			if (lastPing < m_NsVersionRaknetInfoReportConfig.max_ping)
				return;

			if (m_NsVersionRaknetInfoReportConfig.limit_wids.size() > 0)
			{
				if (!GetWorldManagerPtr())
					return;
				long long mapID = GetWorldManagerPtr()->getFromWorldID();
				bool match = false;
				for (std::vector<uint64_t>::iterator ite = m_NsVersionRaknetInfoReportConfig.limit_wids.begin();
					ite != m_NsVersionRaknetInfoReportConfig.limit_wids.end(); ++ite)
				{
					if (*ite == mapID)
					{
						match = true;
						break;
					}
				}
				if (!match)
					return;
			}
		}

		RakNetStatistics staticStatistics;
		if (peer->GetStatistics(hostUin, &staticStatistics) == 0)
			return;
		if (m_NsVersionRaknetInfoReportConfig.open_all != 1)
		{
			uint64_t sendTotal = staticStatistics.runningTotal[USER_MESSAGE_BYTES_SENT] + staticStatistics.runningTotal[USER_MESSAGE_BYTES_RESENT];
			uint64_t resend = staticStatistics.runningTotal[USER_MESSAGE_BYTES_RESENT];
			double lostRate = double(sendTotal) / double(resend);
			if (lostRate < m_NsVersionRaknetInfoReportConfig.max_lost_rate)
				return;
		}
		int lastPing = GetGameNetManagerPtr()->getClientLastPing();
		GetIWorldConfigProxy()->reportRaknetInfo(&staticStatistics);
	}
}

bool RoomClient::updateHeartbeatHttp()
{
#ifndef IWORLD_SERVER_BUILD
	updateNearByHeartBeatLan();
	reportRaknetInfo();
#else
	updateHeartbeatRentTime();
#endif

	if (m_LastHeartbeatReturn >= 0)
	{
		m_LastHeartbeatReturn++;
		if (m_LastHeartbeatReturn >= 60 * 20) //60秒服务器无返回，就断开连接
		{
			LogStringMsg("GAMENET RoomClient::updateHeartbeatHttp:RETURN FLASE");
			return false;
		}
	}

	if (m_HeartbeatTicks >= 0)
	{
		m_HeartbeatTicks++;

		if (m_HeartbeatTicks >= 20 * 20)  //20秒一个心跳包
		{
			reqHostUpdateRoom(GetClientInfoProxy()->getUin(), m_lastStage);
		}
	}
	return true;
}

bool RoomClient::updateHeartbeatLan()
{
	if (mpClientPeer == NULL) return true;

	updateRoomlistLan();

	struct timeval tick;
	gettimeofday(&tick, NULL);

	struct timeval stSub;

	int iPingMs;
	TV_DIFF(stSub, tick, m_lasPingTick);
	TV_TO_MS(iPingMs, stSub);

	if (iPingMs >= LAN_UPDATE_INTERVAL)
	{
		m_lasPingTick = tick;
		if (!m_isHost)
			mpClientPeer->Ping(mRoomServerAddr);
		m_lastRoomSvrPing = mpClientPeer->GetLastPing(mRoomServerAddr);

		if (mpListener)
		{
			mpListener->onLastPing(m_lastRoomSvrPing);
		}

		if (mCurrentRoom.OwnerUin != 0 && mCurrentRoom.LastPing != m_lastRoomSvrPing)
		{
			mCurrentRoom.LastPing = m_lastRoomSvrPing;
			updateRoom(mCurrentRoom.OwnerUin, mCurrentRoom);
		}
	}

	return true;
}

bool RoomClient::updateHeartbeatRentTime()
{
	if (m_HeartbeatRentStartTime == 0)
		return true;

	uint32_t ticks = (Timer::getSystemTick() - m_HeartbeatRentStartTime) / 50;
	// 这里要多种间隔，还是用tick比较好计算，使用时间间隔模拟tick增加
	int count = 20;
	while (ticks > m_HeartbeatTicksRent && count-- > 0)  // 最多执行20次
	{
		updateHeartbeatRent();
	}
	m_HeartbeatTicksRent = ticks;  // 强制对齐 避免卡太多
	if (GetZmqMgrInterface())
		GetZmqMgrInterface()->Tick();
	return true;
}

bool RoomClient::updateHeartbeatRent()
{
#ifdef IWORLD_SERVER_BUILD
	m_HeartbeatTicksRent++;

	if (GetGameInfoProxy()->GetRoomHostType() == ROOM_SERVER_RENT)
	{
		if (m_HeartbeatTicksRent % (20 * 5) == 0) 
		{
			reqHeartbeatRent(0);   //5秒一个心跳包rent
		}
	}
	if (m_HeartbeatTicksRent % (20 * 60) == 0)
	{
		// MINIW::ScriptVM::game()->callFunction("__rent_room_keep_alive__", "iii", PlayerCount, mCurrentRoom.MaxPlayerCount, m_lastPingRent);

		// HttpDownloadMgr::GetInstance().httpPost(upload_url, postdata);
		GameNetManager::getInstance()->actionlog_net_statis();
		GameNetManager::getInstance()->send_net_statis();
		GameNetManager::getInstance()->reset_net_statis();

		GameNetManager::getInstance()->EFF_statis();
	}
	if (GetZmqMgrInterface())
		GetZmqMgrInterface()->Tick();

	if (m_HeartbeatTicksRent % (20 * 1) == 0) 
	{
		static bool s_sync_performance = true;
		
		double CpuUsage = getCpuUsage();
		long long memUsage = getMemoryUsage();
#ifdef DEDICATED_SERVER
		auto memBytes = getProcessMemoryBytes();
		auto memLimit = getProcessMemoryLimit();
		GetICloudProxyPtr()->UpdateHostStat(CpuUsage, memBytes, memLimit);
#endif
		
		if (s_sync_performance)
		{
			long long memMax = getMaxMemory();
			double rxRate = GetProcessRxRate();
			double txRate = GetProcessTxRate();
			//LOG_INFO("cpu:%f,  mem:%lld, mem_max:%lld, rxrate:%f, txrate:%f", CpuUsage, memUsage, memMax, rxRate, txRate);
			s_sync_performance = MINIW::ScriptVM::game()->callFunction("SyncStudioServerPreformance", "dwwdd", CpuUsage, memUsage, memMax, rxRate, txRate);
		}
	}
#endif

	return true;
}

void RoomClient::updateRoomlistLan()
{
	if (nullptr == mpClientPeer)
		return;
	
	RakNet::Packet *packet = mpClientPeer->Receive();
	for (; packet; mpClientPeer->DeallocatePacket(packet), packet = mpClientPeer->Receive())
	{
		switch (GetPacketIdentifier(packet))
		{
		case ID_UNCONNECTED_PONG:
			if (m_isLan)
			{
				RakNet::TimeMS pingtm;
				RakNet::BitStream bsIn(packet->data, packet->length, false);
				bsIn.IgnoreBytes(1);
				bsIn.Read(pingtm);

				RakNet::TimeMS dtime = RakNet::GetTime() - pingtm;

				size_t datalen = packet->length - sizeof(char) - sizeof(RakNet::TimeMS);
				unsigned char *userdata = packet->data + sizeof(unsigned char) + sizeof(RakNet::TimeMS);
				handleRoomMsg(packet, (const PB_PACKDATA*)userdata, datalen);
			}
			break;
		}
	}
}

bool RoomClient::reqHeartbeat(int myUin)
{
	LOG_INFO("@ reqHeartbeat");

	// 心跳计数归零
	m_HeartbeatTicks = 0;

	std::string httpServer;
	if (m_nCurrentRoomOwner == myUin)
	{
		httpServer = getRoomServerUrl(m_ingameRoomServerCfg, 1);
	}
	else
	{
	    httpServer = gethttpServer(m_nCurrentRoomOwner);
	}

	int PlayerCount = mCurrentRoom.PlayerCount;
	int JudgeUin = 0;
	if(GetIClientGameManagerInterface()->getICurGame() && GetIClientGameManagerInterface()->getICurGame()->isInGame())
	{
		JudgeUin = GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame)->getJudgeUin();
		if(JudgeUin)
		{
			PlayerCount--;
		}
	}
	
	std::string s2, s2t;
	GetDefManagerProxy()->getS2(s2, s2t);
	char tokenBuf[128];
	int curtimestamp = (int)(MINIW::GetTimeStamp());
	MINIW::ScriptVM::game()->callFunction("getServerTime", ">i", &curtimestamp);
	std::sprintf(tokenBuf, "%d%s%d", curtimestamp, s2.c_str(), myUin);
	std::string token = gFunc_getmd5(tokenBuf);

	std::string url =
		RoomReqBuilder(httpServer, "/server/room")
		.addParam("cmd", "keep_alive")
		.addParam("count", (m_isHost ? PlayerCount : 0))
		.addParam("ping", m_lastPing)
		.addParam("s2t", s2t.c_str())
		.addParam("time", curtimestamp)
		.addParam("token", token.c_str())
		.addParam("uin", myUin)
		.end();

	LOG_INFO("  url = '%s'", url.c_str());

#if ROOM_USE_NEW_HTTPMANAGER
	UInt32 taskid = ++s_TaskID;
	ROOM_TASK_DATA* task_data = ENG_NEW_LABEL(ROOM_TASK_DATA, kMemGame) (taskid, RT_Heartbeat, "", this);
	
	m_httptask_array[taskid] = Http::GetHttpManager().Request(url, "", task_data, 0, nullptr, HandleRequestEnd);

	//s_RoomTaskArray[this].emplace_back(task_data);
#else
	int taskid = HttpDownloadMgr::GetInstance().rpc(url, "", 0, true);

	m_httpRequests.insert(std::make_pair(taskid, std::make_pair(RT_Heartbeat, "")));
#endif

	m_lastPingSendTime = Rainbow::Timer::getSystemTick();

	return true;
}
#ifdef IWORLD_SERVER_BUILD
static void HandleCloudDelItem(bool success, Rainbow::Http::WebRequest* request)
{
	int result = (success) ? 0 : -2;

	jsonxx::Object* jsonobj = NULL;
	if (result == 0)
	{
		do
		{
			std::string content = request->GetResponse();

			LOG_INFO("  content = `%s`", content.c_str());
			jsonobj = ENG_NEW(jsonxx::Object)();
			if (!jsonobj->parse(content))
			{
				result = -1;
				break;
			}

			if (jsonobj->has<jsonxx::Number>("ret"))
			{
				result = (int)jsonobj->get<jsonxx::Number>("ret");
			}
			else
			{
				result = -1;
				break;
			}
		} while (0);
	}
	ENG_DELETE(jsonobj);
	if (result != 0)
	{
		GetICloudProxyPtr()->SimpleSLOG("roomOwnerConsumePersonalRentroomItem failed, result = %d", result);
	}
}
#endif
int RoomClient::roomOwnerConsumePersonalRentroomItem(int owner_uin)
{
#ifdef IWORLD_SERVER_BUILD
	if (m_alreadyUsePersonalRentRoomItem)
		return (0);
	auto        cliInfo = GetClientInfoProxy();
	const char* env   = cliInfo->getEnterParam("game_env");
	char *baseurl = NULL;
//	http://120.24.64.132:8080/miniw/mall?cmd=cloud_del_item&uin=1000094824&item_id=10023&item_num=1&auth=7b27a132e1f1c8b245bb9b3f79b1ce1f&time=1711098127&map_id=111
	if (env[0] == '0')
	{
		baseurl = "http://shequ.mini1.cn:8087/miniw/mall?cmd=cloud_del_item&item_id=12861&item_num=1&json=1&";
	}
	else if (env[0] == '1')
	{
		baseurl = "http://120.24.64.132:8080/miniw/mall?cmd=cloud_del_item&item_id=12861&item_num=1&json=1&";		
	}
	if (!baseurl)
	{
		GetICloudProxyPtr()->SimpleSLOG("roomOwnerConsumePersonalRentroomItem failed[%d]", owner_uin);
		return -1;
	}
	GetICloudProxyPtr()->SimpleSLOG("personal_rent_room roomOwnerConsumePersonalRentroomItem[%d]", owner_uin);	
	char tokenBuf[128];
	unsigned int curtimestamp = MINIW::GetTimeStamp();
	std::sprintf(tokenBuf, "%u#miniw_907#%d", curtimestamp, owner_uin);
	std::string token = gFunc_getmd5(tokenBuf);
	char requrl[512];
	sprintf(requrl, "%stime=%u&uin=%d&auth=%s&map_id=%s", baseurl, curtimestamp, owner_uin, token.c_str(), cliInfo->getEnterParam("toloadmapid"));
	LOG_INFO("roomOwnerConsumePersonalRentroomItem, url = %s", requrl);

	jsonxx::Object log;
	if (GetZmqMgrInterface())
		log << "roomid" << GetZmqMgrInterface()->GetUinroomid();
	GetICloudProxyPtr()->InfoLog(owner_uin, g_WorldMgr ? g_WorldMgr->getWorldId(): 0, "roomOwnerConsumePersonalRentroomItem", log);
	
	Rainbow::Http::GetHttpManager().Request(requrl, "", nullptr, 0, nullptr, HandleCloudDelItem);

	m_alreadyUsePersonalRentRoomItem = true;
#endif
	return 0;
}

//租赁服务node心跳
bool RoomClient::reqHeartbeatRent(int myUin) {
	//LOG_INFO("@ reqHeartbeatRent");

	int PlayerCount = mCurrentRoom.PlayerCount;

	unsigned int begin_ = Rainbow::Timer::getSystemTick();
#ifdef IWORLD_SERVER_BUILD	
	if (GetClientInfoProxy()->isRentType(1))
	{
		auto        cliInfo = GetClientInfoProxy();
		const char* s_uin   = cliInfo->getEnterParam("account");
		int owner_uin = atoi(s_uin);
		bool owner_in_room = false;
		for (auto it = m_RoomMembers.begin(); it != m_RoomMembers.end(); ++it)
		{
			int uin = (*it);
			if (uin == owner_uin)
			{
				owner_in_room = true;
				break;
			}
		}

			//房主不在房间  记录首次时间a，3分钟后关闭
			//房主在房间    a=0, 3小时后关闭
		if (owner_in_room)
		{
			m_rentOwnerLeaveTick = 0;
			if (GetZmqMgrInterface())
			{
				int initoktime = GetZmqMgrInterface()->GetInitokTime();
				if (initoktime > 0)
				{
					int maxSec = 3 * 3600;
					if (GetZmqMgrInterface()->GetPersonalRoomTimeoutMaxSec() > 0)
						maxSec = GetZmqMgrInterface()->GetPersonalRoomTimeoutMaxSec();
					if (begin_ / 1000 - initoktime > maxSec)
					{
						MINIW::ScriptVM::game()->callFunction("rentLuaEvent", "ss", "personal_close", "");
						roomOwnerConsumePersonalRentroomItem(owner_uin);
						GetICloudProxyPtr()->SimpleSLOG("personal_rent_room closeroom1 [%d], %d,%d,%d",
						                                owner_uin, begin_ / 1000, initoktime, maxSec);
						GetZmqMgrInterface()->CloseRoom(10, "");
						return true;
					}
				}
			}
		}
		else
		{
			if (m_rentOwnerLeaveTick == 0)
			{
				m_rentOwnerLeaveTick = begin_;
			}
			
			int keepSec = 3 * 60;
			if (GetZmqMgrInterface()->GetPersonalRoomTimeoutKeepSec() > 0)
				keepSec = GetZmqMgrInterface()->GetPersonalRoomTimeoutKeepSec();
			if (begin_ - m_rentOwnerLeaveTick >= (keepSec * 1000))
			{
				int initoktime = GetZmqMgrInterface()->GetInitokTime();
				if (initoktime > 0)
				{
					int freeSec = 4 * 60;
					if (GetZmqMgrInterface()->GetPersonalRoomTimeoutFreeSec2() > 0)
						freeSec = GetZmqMgrInterface()->GetPersonalRoomTimeoutFreeSec2();
					if (begin_ / 1000 - initoktime < freeSec)
					{
						MINIW::ScriptVM::game()->callFunction("rentLuaEvent", "ss", "personal_close", "");						
						GetICloudProxyPtr()->SimpleSLOG("personal_rent_room closeroom2 [%d], %d,%d,%d",
						                                owner_uin, begin_ / 1000, initoktime, freeSec);
						GetZmqMgrInterface()->CloseRoom(3, "");
						m_rentOwnerLeaveTick = 0;
						return true;
					}
				}

				roomOwnerConsumePersonalRentroomItem(owner_uin);
				if (GetZmqMgrInterface())
				{
					MINIW::ScriptVM::game()->callFunction("rentLuaEvent", "ss", "personal_close", "");
					GetICloudProxyPtr()->SimpleSLOG("personal_rent_room closeroom3 [%d], %d,%d,%d",
					                                owner_uin, begin_ / 1000, m_rentOwnerLeaveTick / 1000, keepSec);
					GetZmqMgrInterface()->CloseRoom(10, "");
				}
				m_rentOwnerLeaveTick = 0;
				return true;
			}
		}
	}
#endif	
	MINIW::ScriptVM::game()->callFunction("__rent_room_keep_alive__", "iii", PlayerCount, mCurrentRoom.MaxPlayerCount, m_lastPingRent);
	m_lastPingRent = Rainbow::Timer::getSystemTick() - begin_;   //耗时

	return true;
}

void RoomClient::respHeartbeat(int result, jsonxx::Object* jsonobj)
{
	LOG_INFO("respHeartbeat %d", result);
	
	if (RoomErr_NoErr == result)
	{
		m_LastHeartbeatReturn = 0;
		m_lastPing = Rainbow::Timer::getSystemTick() - m_lastPingSendTime;
	}
}

bool RoomClient::reqReportRoomMembers(int myUin, int nStage)
{
	LOG_INFO("@ reqReportRoomMembers");

	std::ostringstream members_str;

#ifndef IWORLD_SERVER_BUILD
	members_str << myUin;  //include the host

	for (auto it = m_RoomMembers.begin(); it != m_RoomMembers.end(); ++it)
	{
		int uin = (*it);
		members_str << "," << uin;
	}

#else
	for (auto it = m_RoomMembers.begin(); it != m_RoomMembers.end(); ++it)
	{
		int uin = (*it);
		if(it != m_RoomMembers.begin())
		members_str << "," << uin;
		else
		members_str << uin;
	}

	//租赁服上报玩家列表
	if (GetGameInfoProxy()->GetRoomHostType() == ROOM_SERVER_RENT) 
	{
		MINIW::ScriptVM::game()->callFunction("rentLuaEvent", "ss", "member_list", members_str.str().c_str());

		if (!m_hasRoomServerIp)
		{
			return true;    //租赁服未配置roomserverip
		}
	}

#endif
	int JudgeUin = 0;
	if(GetIClientGameManagerInterface()->getICurGame()->isInGame())
	{
		JudgeUin = GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame)->getJudgeUin();
	}

	//游戏状态, 0不处理
	if(0 != nStage)
		m_lastStage = nStage;

	std::string url =
		RoomReqBuilder(getRoomServerUrl(m_ingameRoomServerCfg, 1), "/server/room")
		.addParam("cmd", "update_room_member")
		.addParam("locked", m_isLocked ? "1" : "0")
		.addParam("members", members_str.str())
		.addParam("stage", m_lastStage)
		.addParam("uin", myUin)
		.addParam("umpire", JudgeUin)
		.end();

	LOG_INFO("  url = '%s'", url.c_str());

#if ROOM_USE_NEW_HTTPMANAGER
	UInt32 taskid = ++s_TaskID;
	ROOM_TASK_DATA* task_data = ENG_NEW_LABEL(ROOM_TASK_DATA, kMemGame) (taskid, RT_RoomMember, "", this);
	
	m_httptask_array[taskid] = Http::GetHttpManager().Request(url, "", task_data, 0, nullptr, HandleRequestEnd);

	//s_RoomTaskArray[this].emplace_back(task_data);
#else
	int taskid = HttpDownloadMgr::GetInstance().rpc(url, "", 0, true);

	m_httpRequests.insert(std::make_pair(taskid, std::make_pair(RT_RoomMember, "")));
#endif
	
	return true;
}

void RoomClient::respReportRoomMembers(int result, jsonxx::Object* jsonobj)
{
	LOG_INFO("respReportRoomMembers %d", result);
}

void RoomClient::reportRoomJoinable(int myUin, bool locked,std::string memberstr)
{
	if (!m_isHost)
		return;

#ifdef IWORLD_SERVER_BUILD
	if (!GetZmqMgrInterface() || GetZmqMgrInterface()->GetPushServerAddr() == NULL)
		return;
#endif

	int joinable = 1;
	if (locked)
	{
		joinable = 0;
	}
	else
	{
		int count = m_RoomMembers.size();
#ifndef IWORLD_SERVER_BUILD
		count++;
#endif
		int maxcount = mCurrentRoom.MaxPlayerCount;
		if (count >= maxcount)
			joinable = 0;
	}

	if (joinable == m_lastRoomJoinable)
		return;
	m_lastRoomJoinable = joinable;

	/*std::ostringstream members_str;
	bool first_ = true;
#ifndef IWORLD_SERVER_BUILD	
	members_str << myUin;
	first_ = false;
#endif

	for (auto it = m_RoomMembers.begin(); it != m_RoomMembers.end(); ++it)
	{
		int uin = (*it);
		if (first_)
		{
			members_str << uin;
			first_ = false;
		}
		else
		{
			members_str << "," << uin;
		}
	}*/

#ifdef IWORLD_SERVER_BUILD
	auto cliInfo = GetClientInfoProxy();
	const char* s_uin = cliInfo->getEnterParam("account");
	const char* roomid = cliInfo->getEnterParam("room_id");
	char param[512];
	sprintf(param, "&joinable=%d&roomid=%s_%s", joinable, s_uin, roomid);
	// std::string url = "http://***********:19610/minilogic/sendfriend?cmd=roomclient.roomlocked&uinlist=" + memberstr;
	std::string url = GetZmqMgrInterface()->GetPushServerAddr();
	url = url + "minilogic/sendfriend?cmd=roomclient.roomlocked&uinlist=" + memberstr;
	url = url + param;
	Rainbow::Http::GetHttpManager().Request(url, "", nullptr);
	LOG_INFO("reportroomjoinable url = '%s'", url.c_str());
#else
	//sprintf(param, "&joinable=%d&roomid=%u", joinable, myUin);
	//lua_State* L = m_luaScriptVM->getLuaState();
	//GetIClientGameManagerInterface();
	//lua_State* L = LuaInterfaceProxy::GetInstance().m_VM->getLuaState();

	//routefriends = function(self, msgid, uinlist, joinable, roomid)
	MINIW::ScriptVM::game()->callFunction("__routefriends__", "ssii", "roomclient.roomlocked", memberstr.c_str(), joinable, myUin);
#endif

}

bool RoomClient::reqHostUpdateRoom(int myUin, int nStage/* = 0*/, bool force/* = false*/)
{
	//LOG_INFO("@ GAMENET reqHostUpdateRoom");
	// 心跳计数归零
	m_HeartbeatTicks = 0;

	// 非主机不发送
	if (!m_isHost)
		return true;
#ifdef WINDOWS_SERVER
	return true;
#endif

	std::ostringstream members_str;
	members_str << myUin;

	for (auto it = m_RoomMembers.begin(); it != m_RoomMembers.end(); ++it)
	{
		int uin = (*it);
		members_str << "," << uin;
	}	

	std::string memberreportStr = members_str.str();
	reportRoomJoinable(myUin, m_isLocked, memberreportStr);

	// 主机状态是否准备好(官方服默认为：准备好)
	int hostReady = 1;

#ifndef IWORLD_SERVER_BUILD

	if(GetGameNetManagerPtr()->getConnection() == NULL)
	{
		hostReady = 0;
	}else{
		hostReady = GetGameNetManagerPtr()->getConnection()->isHostReady() ? 1 : 0;
	}
#else
	std::ostringstream members_str_rent;
	//租赁服上报玩家列表
	for (auto it = m_RoomMembers.begin(); it != m_RoomMembers.end(); ++it)
	{
		int uin = (*it);
		if(it != m_RoomMembers.begin())
			members_str_rent << "," << uin;
		else
			members_str_rent << uin;
	}

	MINIW::ScriptVM::game()->callFunction("rentLuaEvent", "ss", "member_list", members_str_rent.str().c_str());

	//租赁服未配置roomserverip
	if (GetGameInfoProxy()->GetRoomHostType() == ROOM_SERVER_RENT)
	{
		if (!m_hasRoomServerIp)
		{
			return true;
		}
	}
    if (m_RoomMembers.size())
		members_str_rent << ",";
	members_str_rent << myUin;  // server/room 上报加上房主
#endif

	int JudgeUin = 0;
	auto game_ = GetIClientGameManagerInterface()->getICurGame(GameType::SurviveGame);
	if( game_ && game_->isInGame() )
	{
		JudgeUin = game_->getJudgeUin();
	}

	//游戏状态, 0不处理
	if(0 != nStage)
		m_lastStage = nStage;

	int iProxyConnected = 0;
	UDPConnection* pConn = GameNetManager::getInstance()->getConnection();
	if (pConn && pConn->isHostReady())
		iProxyConnected = 1;

	std::string passwd_md5 = (mCurrentRoom.Password[0] == '\0') ? "" : gFunc_getmd5(mCurrentRoom.Password);
	RoomReqBuilder urlBuilder(getRoomServerUrl(m_ingameRoomServerCfg,1), "/server/room");
	urlBuilder.setIngorTokenMd5Arg("ping"); //设置ping字段的变化不参与 url是否变化的计算
	urlBuilder.addParam("cmd", "host_update_room");
	urlBuilder.addParam("locked", m_isLocked ? "1" : "0");
	urlBuilder.addParam("members", members_str.str());
	urlBuilder.addParam("ping", m_lastPing);
	if (GetClientInfoProxy()->getIsOverseasVer())
	{
		urlBuilder.addParam("proxy_connected", iProxyConnected); //海外独有
	}
	urlBuilder.addParam("ready", hostReady);
	urlBuilder.addParam("stage", m_lastStage);
	urlBuilder.addParam("uin", myUin);
	urlBuilder.addParam("umpire", JudgeUin);
	urlBuilder.calcAuth();
	////国内也加【同步海外切后台pause参数到国内，过滤该类房间】https://www.tapd.cn/22897851/prong/stories/view/1122897851001047361
	urlBuilder.addParam("pause", m_Pause); //海外独有 
#ifndef IWORLD_SERVER_BUILD
	urlBuilder.addParam("can_trace", mCurrentRoom.CanTrace);
	urlBuilder.addParam("public_type", mCurrentRoom.PublicType); //0公开 1 私密;
	urlBuilder.addParam("max_count", mCurrentRoom.MaxPlayerCount);
	urlBuilder.addParam("passwd", passwd_md5);  //string 进入密码(MD5加密);
	urlBuilder.addParam("is_empty_night", m_nDangerNight);  //是否在虚空之夜，1是，0否
#endif

	std::string urlMd5 = urlBuilder.tokenMD5();

	std::string url = urlBuilder.end();
	LOG_INFO("  url = '%s'", url.c_str());

	unsigned int curTime = Rainbow::Timer::getSystemTick();
	if (force || urlMd5 != m_lastHostUpdateTokenMd5 || curTime - m_lastHostUpdateTime > 2000)
	{
#if ROOM_USE_NEW_HTTPMANAGER
		UInt32 taskid = ++s_TaskID;
		ROOM_TASK_DATA* task_data = ENG_NEW_LABEL(ROOM_TASK_DATA, kMemGame) (taskid, RT_HostUpdateRoom, "", this);

		m_httptask_array[taskid] = Http::GetHttpManager().Request(url, "", task_data, 0, nullptr, HandleRequestEnd);

		//s_RoomTaskArray[this].emplace_back(task_data);
#else
		int taskid = HttpDownloadMgr::GetInstance().rpc(url, "", 0, true);

		m_httpRequests.insert(std::make_pair(taskid, std::make_pair(RT_HostUpdateRoom, "")));
#endif

		m_lastHostUpdateTokenMd5 = urlMd5;
		m_lastHostUpdateTime = curTime;
	}
	else
	{
		LOG_INFO(" Repeat request in 2 sec url = '%s'", url.c_str());
	}
	
	return true;
}

void RoomClient::respHostUpdateRoom(int result, jsonxx::Object* jsonobj)
{
	LOG_INFO("GAMENET respHostUpdateRoom %d,%s", result,  jsonobj ? jsonobj->json().c_str() : "");

	if (RoomErr_NoErr == result)
	{
		m_LastHeartbeatReturn = 0;
		m_lastPing = Rainbow::Timer::getSystemTick() - m_lastPingSendTime;

		if (jsonobj != NULL && jsonobj->has<jsonxx::Number>(CustomFlagArg))
		{
			if (jsonobj != NULL && jsonobj->has<jsonxx::Object>("room_info"))
			{
				const jsonxx::Object& roomnode = jsonobj->get<jsonxx::Object>("room_info");
				std::string custom_room_name = roomnode.get<MINIW::String>("custom_room_name", "");
				if (0 == custom_room_name.compare(mCurrentRoom.CustomRoomName))
				{
					this->updateCustomRoomNameFlag((int)jsonobj->get<jsonxx::Number>(CustomFlagArg));
				}
			}
		}
	}
}

bool RoomClient::reqCheckMemberExist(int myUin, int clientUin)
{
	LOG_INFO("@ reqCheckMemberExist");

#ifdef IWORLD_SERVER_BUILD
	//租赁服未配置roomserverip
	if (GetGameInfoProxy()->GetRoomHostType() == ROOM_SERVER_RENT)
	{
		if (!m_hasRoomServerIp)
		{
			return true;
		}
	}
#endif


	std::string url =
		RoomReqBuilder(getRoomServerUrl(m_ingameRoomServerCfg, 1), "/server/room")
		.addParam("cmd", "member_exist")
		.addParam("des_uin", clientUin)
		.addParam("src_uin", myUin)
		.end();

	LOG_INFO("  url = '%s'", url.c_str());

#if ROOM_USE_NEW_HTTPMANAGER
	UInt32 taskid = ++s_TaskID;
	ROOM_TASK_DATA* task_data = ENG_NEW_LABEL(ROOM_TASK_DATA, kMemGame) (taskid, RT_MemberExist, "", this);
	
	m_httptask_array[taskid] = Http::GetHttpManager().Request(url, "", task_data, 0, nullptr, HandleRequestEnd);

	//s_RoomTaskArray[this].emplace_back(task_data);
#else
	int taskid = HttpDownloadMgr::GetInstance().rpc(url, "", 0, true);

	m_httpRequests.insert(std::make_pair(taskid, std::make_pair(RT_MemberExist, "")));
#endif
	
	return true;
}

void RoomClient::respCheckMemberExist(int result, jsonxx::Object* jsonobj)
{
	LOG_INFO("respCheckMemberExist %d", result);

	using namespace jsonxx;
	if (RoomErr_no_room == result)
	{
		// 租赁服测试
	}
	else if (!isLan() && result != 0)
	{
		if(jsonobj){
			int clientUin = (int)jsonobj->get<Number>("des_uin");
			GetGameNetManagerPtr()->getConnection()->kickoffMember(clientUin);
		}
	}
}

// ********: 联机组队需求增加相关接口组队模式的传参  codeby:huangrulin
bool RoomClient::reqReEnterRoom(int hostUin, int connectMode /*= 0*/, std::string genid/* = ""*/)
{
	LOG_INFO("@ reqReEnterRoom");

	int iConnectMode = GetClientInfoProxy()->IsArchiveMapCollaborationMode() ? 1 : 0;
	// ********: 联机组队需求增加相关接口组队模式的传参  codeby:huangrulin
	if (connectMode > 0)
		iConnectMode = connectMode;
	mCurrentRoom.ConnectMode = iConnectMode;

	std::string url =
		RoomReqBuilder(getRoomServerUrl(m_ingameRoomServerCfg, 1), "/server/room")
		.addParam("cmd", "re_enter_room")
		.addParam("uin", hostUin)
		.end();


	LOG_INFO("  url = '%s'", url.c_str());

#if ROOM_USE_NEW_HTTPMANAGER
	UInt32 taskid = ++s_TaskID;
	ROOM_TASK_DATA* task_data = ENG_NEW_LABEL(ROOM_TASK_DATA, kMemGame) (taskid, RT_ReEnterRoom, genid, this);
	m_httptask_array[taskid] = Http::GetHttpManager().Request(url, "", task_data, 0, nullptr, HandleRequestEnd);

	//s_RoomTaskArray[this].emplace_back(task_data);
#else
	int taskid = HttpDownloadMgr::GetInstance().rpc(url, "", 0, true);

	m_httpRequests.insert(std::make_pair(taskid, std::make_pair(RT_ReEnterRoom, genid)));
#endif

	return true;
}

void RoomClient::respReEnterRoom(int result, jsonxx::Object* jsonobj, std::string genid)
{
	LOG_INFO("respReEnterRoom %d", result);

	using namespace jsonxx;
	if (RoomErr_NoErr == result)
	{
		int hostUin = (int)jsonobj->get<Number>("uin");
		int locked = (int)jsonobj->get<Number>("locked");
		if (locked == 1) {
			//ge GetGameEventQue().postRSConnect(14, genid);
			MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
				SetData_Number("result", 14).
				SetData_String("genid", genid).
				SetData_Number("detailreason", -1);
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GIE_RSCONNECT_RESULT", sandboxContext);
			}
		}
		else {
			// TODO: 可以再来一局
			//ge GetGameEventQue().postRSConnect(99, genid);
			MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
				SetData_Number("result", 99).
				SetData_String("genid", genid).
				SetData_Number("detailreason", -1);
			if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
				MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GIE_RSCONNECT_RESULT", sandboxContext);
			}
		}
	}
	else
	{
		//ge GetGameEventQue().postRSConnect(14, genid);
		MNSandbox::SandboxContext sandboxContext = MNSandbox::SandboxContext(nullptr).
			SetData_Number("result", 14).
			SetData_String("genid", genid).
			SetData_Number("detailreason", -1);
		if (MNSandbox::SandboxCoreDriver::GetInstancePtr()) {
			MNSandbox::SandboxEventQueueManager::GetAutoQueue().PushEvent("GIE_RSCONNECT_RESULT", sandboxContext);
		}
	}
}

extern const char *GetWorldThumbPath(char *buf, long long owid, int specialType = NORMAL_WORLD);
extern const char *GetWorldThumbPathForWrite(char *buf, long long owid, int specialType = NORMAL_WORLD);
extern const char *GetWorldThumbPathForWriteWebp(char *buf, long long owid, int specialType = NORMAL_WORLD);

bool RoomClient::reqUploadRoomThumb(long long owid, int specialType)
{
	LOG_INFO("@ reqUploadRoomThumb %lld", owid);

	m_lastUploadThumbOwid = owid;
	MNSandbox::GetGlobalEvent().Emit<long long, int>("OWorldList_respUploadRoomThumb", owid, specialType);
	
	//检查是否可以上传 webp
	//bool is_webp_ = false;
	//UploadTempFileInfo::Type upload_type_ = UploadTempFileInfo::RoomThumb;
	//char path[256] = {0};
	//if (gFunc_isOpenWebp() == 1) {
	//	GetWorldThumbPathForWriteWebp(path, owid, specialType);
	//	if ( MINIW::FileManager::getSingleton().isStdioFileExist( path ) ) {
	//		is_webp_ = true;            //��webp
	//		upload_type_ = UploadTempFileInfo::RoomThumbWebp;
	//	}
	//}

	//if (!is_webp_) {
	//	GetWorldThumbPath(path, owid, specialType);   //��png
	//}

	//OWorldList::GetInstance().m_CSOWorld->uploadTempFile(upload_type_, path, [=](int result, std::string downloadurl){
	//	RoomClient *rc = GameNetManager::getInstance()->getRoomClient();
	//	if(rc){
	//		rc->respUploadRoomThumb(result, downloadurl);
	//	}
	//});

	return true;
}

void RoomClient::respUploadRoomThumb(int result, std::string downloadurl)
{
	LOG_INFO("@ respUploadRoomThumb %d", result);
	if(GetGameNetManagerPtr()->getRoomClient()){
		m_lastUploadThumbUrl = downloadurl;	
		MNSandbox::GetGlobalEvent().Emit<int>("RoomManager_onRoomThumbnailUploaded", result);
	}
}

/*
外网房间--优先查找附近局域网
附近房间最多显示4个
需要跟正常外网房间进行去重操作
不需要向lua那边发消息，ui只要显示最终结果即可，走的还是原来的显示套路ClientAccountMgr--m_RoomList
流程：局域网房间查找（最大个数限制）--外网房间请求--去重、排序--显示
刷新流程相同
*/
// 路由器发送包体
bool RoomClient::updateNearbyLanRoom(const ROOMINFO &room)
{
#ifdef IWORLD_SERVER_BUILD
	return true;
#endif
	if(GetClientInfoProxy()->IsArchiveMapCollaborationMode()) //附近房间-排除协作模式的房间
	{
		return false;
	}

	// ********: 联机组队需求增加相关接口组队模式的传参  codeby:huangrulin
	if (room.ConnectMode == 2)
	{
		return false; //排除组队联机房间
	}

	PB_GetRoomsInfoRes getRoomsInfoRes;
	getRoomsInfoRes.set_resultcode(PB_ROOM_OP_NEARBY_SUCCESS);

	PB_RoomInfo* roomInfo = getRoomsInfoRes.add_roominfolist();
	roomInfo->set_owneruin(room.OwnerUin);
	roomInfo->set_playercount(room.PlayerCount);
	roomInfo->set_maxplayercount(room.MaxPlayerCount);
	roomInfo->set_gametype(room.GameType);
	roomInfo->set_gpslatitude(room.GpsLatitude);
	roomInfo->set_gpslongtitude(room.GpsLongtitude);
	roomInfo->set_timestamp(room.Timestamp);
	roomInfo->set_roomtitle(room.RoomTitle);
	roomInfo->set_ownername(room.OwnerName);
	roomInfo->set_password(room.Password);
	roomInfo->set_description(room.Description);
	roomInfo->set_extradata(room.ExtraData);
	roomInfo->set_lastping(room.LastPing);
	roomInfo->set_province(room.Province);
	roomInfo->set_isp(room.Isp);
	roomInfo->set_gamelabel(room.GameLabel);
	roomInfo->set_owid(room.OWID);
	roomInfo->set_regionip(room.RegionIp);
	roomInfo->set_apiid(room.ApiID);
	
	//添加一个maptype 用来显示地图详情的
	roomInfo->set_maptype(m_strFromOwid);
	roomInfo->set_connectmode(room.ConnectMode);
	roomInfo->set_editorsceneswitch(room.editorSceneSwitch);
	
	// 主要更新附近房间的roomip
	std::string roomip = "";
	int roomport = 8080;
	if(m_ingameRoomServerCfg == NULL){
		int apiid = GetClientInfoProxy()->GetAppId();
		if((apiid == 303) || (apiid == 310)){
			roomip = "**************";
		}else{
			roomip = "*************";
		}
	}
	else
	{
#ifndef IWORLD_SERVER_BUILD
		roomip = m_ingameRoomServerCfg->roomhosthttp_url;
		roomport = m_ingameRoomServerCfg->roomhosthttp_port;
#else
//		roomip = ClientUrl::GetRoomServerUrl().c_str();
#endif
	}
	roomInfo->set_roomip(roomip);
	roomInfo->set_roomport(roomport);
	
	roomInfo->set_gameenv(GetIWorldConfigProxy()->getGameData("game_env"));

	PB_PACKDATA packData(PB_GET_ROOMS_INFO_RES, getRoomsInfoRes.ByteSize());
	getRoomsInfoRes.SerializeToArray(packData.MsgData, packData.ByteSize);
	GetGameNetManagerPtr()->getConnection()->setPingResponse((const char*)&packData, packData.ByteSize + PB_PROTO_HEAD_LEN);
	return true;
}
//连接房间主机
bool RoomClient::connectNearbyLan()
{
	//assert(mpClientPeer == NULL);
    if (mpClientPeer) {
        WarningStringMsg("%s() : assert(mpClientPeer == nullptr): %p", __FUNCTION__, mpClientPeer);
    }
	mpClientPeer = RakNet::RakPeerInterface::GetInstance();
	m_isLan = false;
	/* //20220528 huangrulin ios上此处Startup的时候 会卡好长时间，目前已经没有使用 附近房间这个功能了，注释掉
	//RakNet::SocketDescriptor tmpSockDesc(LAN_CLIENT_PORT, 0);
	RakNet::SocketDescriptor tmpSockDesc(0, 0);
	RakNet::StartupResult startupResult = mpClientPeer->Startup(1, &tmpSockDesc, 1);

	if(RakNet::RAKNET_STARTED != startupResult && RakNet::RAKNET_ALREADY_STARTED != startupResult)
	{
		//TODO log failed
		LOG_INFO("Connect roomserver init failed");
		return false;
	}
	return true;
		*/
	return false;
}
// 更新附近包体（仿照原来热点功能）
bool RoomClient::updateNearByHeartBeatLan()
{
	if (mpClientPeer == NULL) return true;
	updateNearbyRoomlistLan();
	return true;
}
// 接收附近包体
void RoomClient::updateNearbyRoomlistLan()
{
	if (nullptr == mpClientPeer)
		return;
	
	RakNet::Packet *packet = mpClientPeer->Receive();
	for (; packet; mpClientPeer->DeallocatePacket(packet), packet = mpClientPeer->Receive())
	{
		RakNet::MessageID result = GetPacketIdentifier(packet);
		switch (result)
		{
		case ID_UNCONNECTED_PONG:
			RakNet::TimeMS pingtm;
			RakNet::BitStream bsIn(packet->data, packet->length, false);
			bsIn.IgnoreBytes(1);
			bsIn.Read(pingtm);

			RakNet::TimeMS dtime = RakNet::GetTime() - pingtm;

			size_t datalen = packet->length - sizeof(char) - sizeof(RakNet::TimeMS);
			unsigned char *userdata = packet->data + sizeof(unsigned char) + sizeof(RakNet::TimeMS);
			// 收到了包体肯定要处理一下
			// 这里顺序可能要修正一下 先处理这个包体，再请求正常的外网房间
			handleNearbyRoomMsg(packet, (const PB_PACKDATA*)userdata, datalen);
			break;
		}
	}
}
// 处理收到的附近包体
bool RoomClient::handleNearbyRoomMsg(RakNet::Packet *packet, const PB_PACKDATA* packData, size_t len)
{
	if (nullptr == packData || len <= 0) return false;
	if (nullptr == mpListener) return false;

	if (packData->MsgCode == PB_GET_ROOMS_INFO_RES)
	{
		PB_GetRoomsInfoRes getRoomsInfoRes;
		getRoomsInfoRes.ParseFromArray(packData->MsgData, packData->ByteSize);

		mpListener->onNearByGetRoomsRes(getRoomsInfoRes);
		/*if (!m_isLan && getRoomsInfoRes.resultcode() == PB_ROOM_OP_NEARBY_SUCCESS && getRoomsInfoRes.roominfolist_size() > 0)
		{
			m_RoomHostAddrs[getRoomsInfoRes.roominfolist(0).owid()] = packet->systemAddress;
		}*/
	}
	return true;
}

void RoomClient::searchNearByUins(int* uins, int num)
{
	LOG_INFO("@ searchNearByUins");
	m_searchNearByUins.clear();
	m_searchNearByUinResults.clear();

	m_uinRoomServerCfg.clear();
	for (int i = 0; i < num; i++)
	{
		int uin = uins[i];
		m_searchNearByUins.push_back(uin);
	}

	reqSearchNextNearByUin();
}
void RoomClient::reqSearchNextNearByUin()
{
	if (m_searchNearByUins.empty())
	{
		searchNearByUinFinish();
		return;
	}
	int uin = m_searchNearByUins[0];
	m_searchNearByUins.erase(m_searchNearByUins.begin());
	LOG_INFO("@ reqSearchNextNearByUin: %d", uin);
	if (uin < 1000)
	{
		m_searchNearByUinResults.push_back(std::make_pair(RoomErr_InvalidUin, RoomDesc()));
		return;
	}
	std::string url =
		RoomReqBuilder(getRoomServerUrl(getRoomServerDefault()), "/server/room")
		.addParam("cmd", "query_role_room")
		.addParam("des_uin", uin)
		.addParam("src_uin", GetClientInfoProxy()->getUin())
		.end();

	LOG_INFO("  url = '%s'", url.c_str());

#if ROOM_USE_NEW_HTTPMANAGER
	UInt32 taskid = ++s_TaskID;
	ROOM_TASK_DATA* task_data = ENG_NEW_LABEL(ROOM_TASK_DATA, kMemGame) (taskid, RT_NearByRoomSearch, "", this);

	m_httptask_array[taskid] = Http::GetHttpManager().Request(url, "", task_data, 0, nullptr, HandleRequestEnd);

	//s_RoomTaskArray[this].emplace_back(task_data);
#else
	int taskid = HttpDownloadMgr::GetInstance().rpc(url);

	m_httpRequests.insert(std::make_pair(taskid, std::make_pair(RT_NearByRoomSearch, "")));
#endif
	
	m_lastPingSendTime = Rainbow::Timer::getSystemTick();
}
void RoomClient::searchNearByUinFinish()
{
	LOG_INFO("@ searchNearByUinFinish");

	std::vector<RoomDesc> rooms;
	for (int i = 0; i < (int)m_searchNearByUinResults.size(); i++)
	{
		int result = m_searchNearByUinResults[i].first;
		RoomDesc rm = m_searchNearByUinResults[i].second;
		if (result == 0)
			rooms.push_back(rm);
	}
	if (rooms.size() > 0)
	{
		MNSandbox::GetGlobalEvent().Emit<int, RoomDesc*, int>("RoomManager_onUpdateNearByHttp", 0, rooms.data(), rooms.size());
	}
	else
	{
		MNSandbox::GetGlobalEvent().Emit<int, RoomDesc*, int>("RoomManager_onUpdateNearByHttp", RoomErr_no_room, NULL, 0);
	}

	MNSandbox::GetGlobalEvent().Emit<const int, bool>("RoomManager_onLastPing", m_lastPing, true);
}
void RoomClient::respSearchNearByUinRoom(int result, jsonxx::Object* jsonobj)
{
	LOG_INFO("respSearchNearByUinRoom %d", result);

	using namespace jsonxx;

	bool succeed = false;
	bool searchNextUin = true;

	if (RoomErr_NoErr == result)  //在本服
	{
		m_lastPing = Rainbow::Timer::getSystemTick() - m_lastPingSendTime;

		if (jsonobj != NULL && jsonobj->has<jsonxx::Object>("room_info"))
		{
			const jsonxx::Object& roomnode = jsonobj->get<jsonxx::Object>("room_info");

			RoomDesc rm;
			parseRoomDesc(roomnode, rm);

			succeed = true;

			rm.from_http = true;

			m_searchNearByUinResults.push_back(std::make_pair(0, rm));
		}
		else
			result = RoomErr_MissingParams;
	}
	else if (RoomErr_other_room == result)  //在他服
	{
		m_lastPing = Rainbow::Timer::getSystemTick() - m_lastPingSendTime;

		succeed = true;
		searchNextUin = false;				

		if (jsonobj != NULL && jsonobj->has<Number>("uin") && jsonobj->has<Number>("port") && 
			jsonobj->has<MINIW::String>("ip") &&
			jsonobj->has<Number>("port") &&
			jsonobj->has<MINIW::String>("p_ip") &&
			jsonobj->has<Number>("p_port") &&
			jsonobj->has<MINIW::String>("pr_ip") &&
			jsonobj->has<Number>("pr_port"))
		{
			GameNetCfg uinNetCfg;
			uinNetCfg.roomhosthttp_port = (int)jsonobj->get<Number>("port");
			uinNetCfg.roomhosthttp_url = jsonobj->get<MINIW::String>("ip");
			uinNetCfg.roomhosthttp_port = (int)jsonobj->get<Number>("port");
			uinNetCfg.natpunch_url = jsonobj->get<MINIW::String>("p_ip");
			uinNetCfg.natpunch_port = (int)jsonobj->get<Number>("p_port");
			uinNetCfg.proxy_url = jsonobj->get<MINIW::String>("pr_ip");
			uinNetCfg.proxy_port = (int)jsonobj->get<Number>("pr_port");

			int uin = (int)jsonobj->get<Number>("uin");
			m_uinRoomServerCfg[uin] = uinNetCfg;

			reqGetNearByUinRoom(uin, uinNetCfg);
		}
		else
		{
			LOG_INFO("respSearchUinRoom: failed, json missing params");
		}
	}

	if (!succeed)
	{
		m_searchNearByUinResults.push_back(std::make_pair(result, RoomDesc()));
	}

	if (searchNextUin)
		reqSearchNextNearByUin();
}
void RoomClient::reqGetNearByUinRoom(int uin, const GameNetCfg& cfg)
{
	LOG_INFO("@ reqGetNearByUinRoom: %d ip='%s', port=%d", uin, cfg.roomhosthttp_url.c_str(), cfg.roomhosthttp_port);

	std::string url =
		RoomReqBuilder(getRoomServerUrl(&cfg, 1), "/server/room")
		.addParam("cmd", "query_room_info")
		.addParam("des_uin", uin)
		.addParam("src_uin", GetClientInfoProxy()->getUin())
		.end();

	LOG_INFO("reqGetNearByUinRoom  url = '%s'", url.c_str());

#if ROOM_USE_NEW_HTTPMANAGER
	UInt32 taskid = ++s_TaskID;
	ROOM_TASK_DATA* task_data = ENG_NEW_LABEL(ROOM_TASK_DATA, kMemGame) (taskid, RT_GetNearByUinRoom, "", this);

	m_httptask_array[taskid] = Http::GetHttpManager().Request(url, "", task_data, 0, nullptr, HandleRequestEnd);

	//s_RoomTaskArray[this].emplace_back(task_data);
#else
	int taskid = HttpDownloadMgr::GetInstance().rpc(url);

	m_httpRequests.insert(std::make_pair(taskid, std::make_pair(RT_GetNearByUinRoom, "")));
#endif
}
void RoomClient::respGetNearByUinRoom(int result, jsonxx::Object* jsonobj)
{
	LOG_INFO("respGetNearByUinRoom %d", result);

	using namespace jsonxx;

	bool succeed = false;

	if (RoomErr_NoErr == result)  //在本服
	{
		if (jsonobj != NULL && jsonobj->has<jsonxx::Object>("room_info"))
		{
			const jsonxx::Object& roomnode = jsonobj->get<jsonxx::Object>("room_info");

			RoomDesc rm;
			parseRoomDesc(roomnode, rm);

			succeed = true;

			rm.from_http = true;

			m_searchNearByUinResults.push_back(std::make_pair(0, rm));
		}
		else
			result = RoomErr_MissingParams;
	}

	if (!succeed)
	{
		m_searchNearByUinResults.push_back(std::make_pair(result, RoomDesc()));
	}

	reqSearchNextNearByUin();
}

void RoomClient::reqGetManorUinRoom(int uin, const GameNetCfg& cfg)
{
	LOG_INFO("@ reqGetManorUinRoom: %d ip='%s', port=%d", uin, cfg.roomhosthttp_url.c_str(), cfg.roomhosthttp_port);
	std::string url =
		RoomReqBuilder(getRoomServerUrl(&cfg, 1), "/server/room")
		.addParam("cmd", "query_role_manor_room")
		.addParam("des_uin", uin)
		.addParam("src_uin", GetClientInfoProxy()->getUin())
		.end();
	LOG_INFO("reqGetManorUinRoom  url = '%s'", url.c_str());
#if ROOM_USE_NEW_HTTPMANAGER
	UInt32 taskid = ++s_TaskID;
	ROOM_TASK_DATA* task_data = ENG_NEW_LABEL(ROOM_TASK_DATA, kMemGame) (taskid, RT_GetManorUinRoom, "", this);

	m_httptask_array[taskid] = Http::GetHttpManager().Request(url, "", task_data, 0, nullptr, HandleRequestEnd);

	//s_RoomTaskArray[this].emplace_back(task_data);
#else
	int taskid = HttpDownloadMgr::GetInstance().rpc(url);

	m_httpRequests.insert(std::make_pair(taskid, std::make_pair(RT_GetManorUinRoom, "")));
#endif
	
}
void RoomClient::respGetManorUinRoom(int result, jsonxx::Object* jsonobj)
{
	LOG_INFO("respGetManorUinRoom %d", result);

	using namespace jsonxx;
	RoomDesc rm;
	if (RoomErr_NoErr == result)  //在本服
	{
		m_lastPing = Rainbow::Timer::getSystemTick() - m_lastPingSendTime;
		if (jsonobj != NULL && jsonobj->has<jsonxx::Object>("room_info"))
		{
			const jsonxx::Object& roomnode = jsonobj->get<jsonxx::Object>("room_info");
			parseRoomDesc(roomnode, rm);
			rm.from_http = true;
			m_searchUinResults.push_back(std::make_pair(0, rm));

			LOG_INFO("respManorRoom get roomdesc");

		}
		else
			result = RoomErr_MissingParams;
	}
	MNSandbox::GetGlobalEvent().Emit<int, RoomDesc>("RoomManager_onGetManorRoom", result, rm);
}

RakNet::SystemAddress *RoomClient::findRentHostAddrByUin(const char* rentserverid)
{
	auto iter = m_RentRoomHostAddrs.find(rentserverid);
	if(iter != m_RentRoomHostAddrs.end())
	{
		return &iter->second;
	}
	else return NULL;
}

void RoomClient::addRentHostAddrByUin(const char* rentserverid, const char* str, unsigned short port)
{
	m_RentRoomHostAddrs[rentserverid] = RakNet::SystemAddress(str, port);
}
RoomDesc::~RoomDesc()
{
	//2021/07/15 崩溃修复 codeby:chengjie
	if (MINIW::ScriptVM::game())
	{
		tolua_clear_tousertype(MINIW::ScriptVM::game()->getLuaState(), this, "RoomDesc");
	}
}

void RoomClient::ResetConfigPulled()
{
	s_serverConfigPulled = false;
	s_serverConfigPulledTime = 0;
}

void RoomClient::MarkConfigPulled()
{
	s_serverConfigPulled = true;
	s_serverConfigPulledTime = (int)(MINIW::GetTimeStamp());
}

bool RoomClient::IsConfigPulledOutTime()
{
	int minRange = 600;
	int invalidTime = 0;
	int curTime = (int)(MINIW::GetTimeStamp());
	MINIW::ScriptVM::game()->callFunction("G_GetRoomServerConfigOutTimeRange", ">i", &invalidTime);
	if (invalidTime < minRange)
	{
		invalidTime = minRange;
	}

	return curTime > s_serverConfigPulledTime + invalidTime;
}

int RoomClient::GetRegApiid()
{
	int regApiid = 0;
	MINIW::ScriptVM::game()->callFunction("GetRegApiid", ">i", &regApiid);
	return regApiid;
}

std::string RoomClient::GetRoomInfoToken()
{
	jsonxx::Object* jsonobj = ENG_NEW(jsonxx::Object)();
	std::string infoToken = "";
	do
	{
		if (!jsonobj->parse(mCurrentRoom.ExtraData))
			break;

		if (!jsonobj->has<jsonxx::String>("uniqueCode"))
			break;

		infoToken = jsonobj->get<jsonxx::String>("uniqueCode");

	} while (false);

	OGRE_DELETE(jsonobj);

	if (infoToken.empty())
	{
		std::ostringstream sstr;
		sstr << mCurrentRoom.OwnerUin;
		infoToken = sstr.str();
	}

	return infoToken;
}

std::string RoomClient::GetEnterRoomSceneID()
{
	char sceneID[512] = { 0 };
	MINIW::ScriptVM::game()->callFunction("GetStandEnterRoomSceneID", ">s", sceneID);
	std::stringstream stream;
	stream << sceneID;
	std::string strSceneID = stream.str();
	return  strSceneID;
}


void RoomClient::updateCustomRoomNameFlag(int flag)
{
	if (mCurrentRoom.CustomRoomName[0] != '\0' && flag != mCurrentRoom.customRoomNameFlag)
	{
		mCurrentRoom.customRoomNameFlag = flag;
		SandboxCoreDriver::GetInstance().GetManagers().GetEventDispatcherMgr().Emit(
			"RoomClient_CustomNameFlag",
			SandboxContext(nullptr).SetData_Number("flag", mCurrentRoom.customRoomNameFlag).SetData_String("nameStr", mCurrentRoom.CustomRoomName)
		);
	}
}

void RoomClient::onPause()
{
	m_Pause = 1;
	reqHostUpdateRoom(GetClientInfoProxy()->getUin());
}

void RoomClient::onResume()
{
	m_Pause = 0;
	reqHostUpdateRoom(GetClientInfoProxy()->getUin());
}

void RoomClient::setIsDangerNight(unsigned short nDanger)
{
	if (nDanger != m_nDangerNight)
	{
		m_nDangerNight = nDanger;
	}
}
