#include "ClientLogin.h"
#include "MiniReportMgr.h"
#include "IWorldConfig.h"
#include "Utilities/Word.h"
#include "MacroControlStatistics.h"//#include "StatisticsTools.h"
#include "Platforms/PlatformInterface.h"
#include "ClientInterface.h"
#include "File/FileManager.h"
#include "base/CCDirector.h"
#include "ClientInfo.h"
#include "LoginReport.h"
#include "OgreScriptLuaVM.h"
#include "GameLoader.h"
#include "GameRuntime.h"
#include "PlatformUtility.h"
#include "CommonUtil.h"
#include "LuaInterface.h"
#include "version.h"
//#include "PluginConfig.h"
//#include "IworldSystemConfig.h"
#include "SandboxCore.h"
#include "SandboxCoreDriver.h"
#include "SandboxRuntime.h"
#include "PluginManager.h"
#include "SandBoxManager.h"
#include "SandBoxUtilsManager.h"
#include "SandboxMacros.h"
#include "Misc/TimeManager.h"
#include "ClientGameManager.h"
//#include "ShortcutModule.h"
#include "Clock.h"
#include "Input/InputManager.h"
//#include "FunnyCore.h"
#include "ClientApp.h"
#include "ClientGame.h"
#include "OgreTimer.h"
#include "defmanager.h"
#include "ModEditorManager.h"
#include "OgreModFileManager.h"
#include "ModManager.h"
#include "GameUI.h"
#include "CraftMgr.h"
#include "BluePrintMgr.h"
#include "gameInfo/ClientInfo.h"
#include "GetClientInfo.h"
#include "LoadStepCounter.h"
#include "PlatformSdkManager.h"
#if PLATFORM_WIN
#include "AntiPluginHandle.h"
#include "DevConfigurationInfo.h"
#include <Windows.h>
#include <fstream>
#include "FairyGUI/Cocos2dx/platform/win32/CCUtils-win32.h"
#endif
#include "ClientInfo.h"
#include "Pkgs/PkgUtils.h"
#include "Libfairygui/UISkinColorManager.h"
#include "UILib/ui_framemgr.h"
#include "ScriptVM/LuaGuard.h"
#include "uicontrol/CustomViewRegister.h"
#ifdef IWORLD_SERVER_BUILD
#include "ClientAccount.h"
//#include "MiniWorldPreferences.h"
#include "ZmqProxy.h"
#include "ClientGameStandaloneServer.h"
#endif

#include "Bootstrap/BootConfig.h"
#include "MiniHotfix/HotFixScene.h"
#include "UILib/UIRenderer.h"
#include "Render/ShaderMaterial/Utilities.h"
#include "Debug/DebugMgr.h"
#include "Misc/GameSetting.h"
#include "GlobalFunctions.h"
#include "GameZoneCsv.h"
#include "GameLanguageCsv.h"
#include "IModuleConf.h"
#include "MultiLocalMgr.h"
#include "MiniPlatform/StringSafe/keywordfilter/KeywordFilterMgr.h"
#include "TextRendering/FontAtlasCache.h"
#include "VMProtect.h"
#include "Libfairygui/UIConfig.h"
#include "CameraManager.h"
#include "Res/MultiResPath.h"	//LL:********:多资源路径管理器
#if PLATFORM_ANDROID
#include "Platforms/Android/GameBaseActivity.h"
#include "Platforms/Android/AppPlayJNI.h"
#endif

#include "MiniMutualParams.h"
#include "appupdate/AppUpdateScene.h"
#include "appupdate/LaunchScene.h"
#include "MiniHotfix/BaseAppUpdateScene.h"
#include "MiniHotfix/DnsConvertScene.h"
#include "SandboxNodeRegister.h"
#include "AdventureGuideMgr.h"

#ifdef MODULE_FUNCTION_ENABLE_STATISTICS
#include "GameStatistics/StatisticsTools.h"
#include "GameStatistics/GameStatistic.h"
#include "GameStatistics/TerrgenStatistic.h"
#endif

#include "GfxDevice/GfxDeviceSetting.h"
#include "MiniHotfix/HotfixLocalUnit.h"
#include "Configuration/GameConfigMgr.h"
using namespace Rainbow;
using namespace Rainbow::UILib;
extern int tolua_open_all(lua_State* tolua_S);

#if PLATFORM_OHOS
#include <Platforms/OHOS/napi/file/FileUtils.h>
void temp__CopyPkgToWritePath(const core::string& pkg); // implemented in ClientApp.cpp
#endif

namespace MINIW
{

#pragma region API

	int getGameData(const char* name) {
		return GetIWorldConfig().getGameData(name);
	}

	bool m_bFirstEnter = false;
	//true就是第一次登录
	void judgeFirstEnter() {
		XMLNode node = GetIWorldConfig().getRootNode().getOrCreateChild("GameData");
		m_bFirstEnter = !node.hasChild("Statistics");

	}

#pragma endregion API

	static int tolua_GetClientInfo(lua_State* L) {
		tolua_pushusertype(L, GetClientInfo(), GetClientInfo()->GetUserType());
		return 1;
	}

	//重载print
	static luaL_Reg game_global_funcs[] = {
		{ "GetClientInfo", tolua_GetClientInfo },
		{ NULL, NULL }
	};

	//游戏退出的时候会调用
	ClientLogin::~ClientLogin()
	{
		GetDebugMgr().EndGame();
		CommonUtil* cu = CommonUtil::GetInstancePtr();
		ENG_DELETE(cu);
		//ENG_DELETE(m_pAntiPluginHandle);
		//ENG_DELETE(m_DevConfigurationInfo);
		PlatformUtility::destroy();
	}

	void ClientLogin::SetDevelopmentVersion()
	{
		std::string gitcfgContext = "";
		std::string email = "";
		std::string userName = "";
#if PLATFORM_WIN
		wchar_t cUserNameBuffer[256];
		DWORD dwUserNameSize = 256;

		/* Retrieves the name of the user associated with the current thread. */
		if (GetUserName(cUserNameBuffer, &dwUserNameSize))
		{
			userName = cocos2d::StringWideCharToUtf8(cUserNameBuffer);
			//}
			//std::string gitConfigFilePath = "C:/Users/";
			//gitConfigFilePath.append(userName);
			//gitConfigFilePath.append("/");
			//gitConfigFilePath.append("/.gitconfig");
			//std::ifstream in(gitConfigFilePath.c_str());
			//std::string query;
			//email = userName;
			//while (getline(in, query))
			//{
			//	
			//	if (query.find("email")!=query.npos)
			//	{
			//		email = query;
			//		size_t offset =query.find("=");
			//		if (query.find("=") != query.npos)
			//		{
			//			email = query.substr(offset+1,query.npos);
			//		}
			//		
			//		break;
			//	}
		}
		//char buffer[1024] = { "\0" };
		//FILE* pf = NULL;
		//std::string cmdline(R"(git config user.email)");
		//pf = _popen(cmdline.c_str(), "r");
		//if (NULL != pf)
		//{
		//	while (fgets(buffer, sizeof(buffer), pf))
		//	{
		//		email += buffer;
		//	}

		//}
		//_pclose(pf);



		SECURITY_ATTRIBUTES sa;
		HANDLE hRead, hWrite;
		sa.nLength = sizeof(SECURITY_ATTRIBUTES);
		sa.lpSecurityDescriptor = NULL;
		sa.bInheritHandle = TRUE;

		if (!CreatePipe(&hRead, &hWrite, &sa, 0)) {
			DWORD ret = GetLastError();
			ErrorStringMsg("Get Git Email Error");
		}

		STARTUPINFO si;
		PROCESS_INFORMATION pi;
		ZeroMemory(&si, sizeof(STARTUPINFO));

		si.cb = sizeof(STARTUPINFO);
		GetStartupInfo(&si);
		si.hStdError = hWrite;
		si.hStdOutput = hWrite;
		si.wShowWindow = SW_HIDE;
		si.dwFlags = STARTF_USESHOWWINDOW | STARTF_USESTDHANDLES;
		TCHAR commandLine[] = TEXT("git config user.email");
		if (!CreateProcess(NULL, commandLine, NULL, NULL, TRUE, NULL,
			NULL, NULL, &si, &pi)) {
			DWORD ret = GetLastError();
			CloseHandle(hRead);
			CloseHandle(hWrite);
			ErrorStringMsg("Get Git Email Error，because CreateProcess fail");
		}

		CloseHandle(hWrite);
		char buffer[4096] = { 0 };
		DWORD bytesRead;
		while (true) {
			if (!ReadFile(hRead, buffer, 4095, &bytesRead, NULL)) break;
			email.append(buffer, bytesRead);
			Sleep(100);
		}

		DWORD exitCode = 0;
		GetExitCodeProcess(pi.hProcess, &exitCode);
		CloseHandle(hRead);
		CloseHandle(pi.hThread);
		CloseHandle(pi.hProcess);

#endif
		if (email.empty())
		{
			email = userName;
#if WATER_MARK && !BUILD_MINI_EDITOR_APP
			if (COMPILE_VER_STR != "0")
			{
				email = COMPILE_VER_STR + std::string("_develop");
			}
#endif
		}
			
		cocos2d::Director::getInstance()->setWaterMarkInfo(email.c_str());
	}

	bool ClientLogin::LoginStep1() {

		VMP_BEGIN("ClientLogin_LoginStep1");
		//游戏的环境参数
		int game_env = getGameData("game_env");
		Mini::GetHttpReportMgr().setEnv(game_env);
		judgeFirstEnter();

		core::string langStr = IntToString(getGameData("lang")); //这里语言还没初始化好，这里的语言不做上报，提前调getGameData("lang")会导致切语言问题 code_by:huangfubin 2022.8.1
		const std::string& deviceId = GetClientInfo()->getDeviceID();
		STATISTICS_INTERFACE_EXEC(gameAction(SAID_GAMELOAD1, 3, deviceId.c_str(), m_bFirstEnter ? "1" : "2", langStr.c_str()), 0);

		MINIW::SetClientEnv(game_env);
		//PkgUtils::InitHotFixPkg(); //热更新的包
		//PkgUtils::InstallPatch(game_env);

		Rainbow::FileManager& fileManager = GetFileManager();
		fileManager.CreateWritePathDir("data/http/ma/");          //martet activity运营活动
		fileManager.CreateWritePathDir("data/http/thumbs/");      //地图缩略图
		fileManager.CreateWritePathDir("data/http/thumbs_room/"); //房间缩略图
		fileManager.CreateWritePathDir("data/http/update/");      //以后动态更新预留
		fileManager.CreateWritePathDir("data/http/config/");      //config xml目录
		fileManager.CreateWritePathDir("data/mods/");             //mod下载目录
		fileManager.CreateWritePathDir("data/http/photo/");       //玩家头像
		fileManager.CreateWritePathDir("data/http/mods/");         //房间mod
		fileManager.CreateWritePathDir("data/http/pixelmap/");         //小地图
		fileManager.CreateWritePathDir("data/http/customui/");         //UILib
		fileManager.CreateWritePathDir("data/http/custommodels/");  //自定义模型资源
		fileManager.CreateWritePathDir("data/http/productions/");  //模型部件
		fileManager.CreateWritePathDir("data/arcamera/");			//ar模型贴图
		fileManager.CreateWritePathDir("data/http/skins/");
		fileManager.CreateWritePathDir("data/http/intros/");		// 开局介绍
		fileManager.CreateWritePathDir("data/http/modelcache/");
		fileManager.CreateWritePathDir("data/http/custompic/");    //自定义图片
		fileManager.CreateWritePathDir("data/http/weapon/");	//weapon path  2021.11.03 hanhuihua
		fileManager.CreateWritePathDir("data/http/qqmusic/");		 //QQ播放器
		fileManager.CreateWritePathDir("data/http/customaudio/");  //自定义音频
		fileManager.CreateWritePathDir("data/http/video/");  //本地视频
		fileManager.CreateWritePathDir("data/ugcres/vbp/official/");  //官方蓝图
		fileManager.CreateWritePathDir("data/http/ugcassets/");  //新格式资源
		// TODO  这个installPatch
		//MINIW::FileManager::GetInstance().installPatch(env_);
		GetClientInfo()->SetAppState(APPSTATE_CREATED);
		LoginReport::ReportUserInfo();

		GameAnalytics::InitCommonProps();
		 
		
#ifdef IWORLD_UNIVERSE_BUILD
		Mini::GetHttpReportMgr().reportOverseaAppActive();
#endif
		VMP_END;
		return true;
	}

	void ClientLogin::StartLaunch(bool isEnableColdUpdate)
	{
#ifdef IWORLD_UNIVERSE_BUILD
		auto scene = LaunchScene::create();
		scene->loadNative(isEnableColdUpdate);
		//scene->autorelease();
		cocos2d::Director::getInstance()->replaceScene(scene);
#endif
	}

	void ClientLogin::StartAppUpdate(bool isSkipColdUpdate)
	{
#ifdef IWORLD_UNIVERSE_BUILD
		GetClientInfo()->SetAppState(APPSTATE_HOTFIXING);
		GetInputManager().RegisterInputHandler(GetGameUIPtr());
		GetGameUIPtr()->SetOldUIEnable(false);
		auto scene = AppUpdateScene::create();
		cocos2d::Director::getInstance()->replaceScene(scene);
		if (isSkipColdUpdate)
		{
			scene->CheckHotfixUpdate();
		}
		else
		{
			scene->CheckColdUpdate();
		}
#endif // IWORLD_UNIVERSE_BUILD
	}

	void ClientLogin::StartHotfix(bool NeedAskForHotfix)
	{
#if PLATFORM_ANDROID
    #ifdef IWORLD_UNIVERSE_BUILD
    #else
		WarningStringMsg("StartDetectionPermission!!!");
		GameBaseActivity::StartDetectionPermission();
    #endif //IWORLD_UNIVERSE_BUILD
#endif // PLATFORM_ANDROID

		GetClientInfo()->SetAppState(APPSTATE_HOTFIXING);
		GetInputManager().RegisterInputHandler(GetGameUIPtr());
		GetGameUIPtr()->SetOldUIEnable(false);
		////打开热更新的界面在这里写
		auto scene = HotFixScene::create();
		cocos2d::Director::getInstance()->replaceScene(scene);

		scene->StartHotfix();
	}

	/* --------------------------------------------------------------------------
	* 启动清理:清理历史不可用补丁包
	*/
	void  ClientLogin::ClearHistoryUpdate()
	{
		//清理过期或脏数据
		BaseHotFixService::ClearExpireOrDirtyFile();
	}

	void ClientLogin::SetWaterMark(const std::string& message)
	{
#if WATER_MARK && !DEDICATED_SERVER && !BUILD_MINI_EDITOR_APP
		cocos2d::Director::getInstance()->setWaterMarkInfo(message.c_str());
#endif
	}

	void ClientLogin::StartUniverseAppUpdate(bool coldUpdate, bool hotfix)
	{
		GetClientInfo()->SetAppState(APPSTATE_HOTFIXING);
		GetInputManager().RegisterInputHandler(GetGameUIPtr());
		GetGameUIPtr()->SetOldUIEnable(false);
		auto scene = BaseAppUpdateScene::create();
		cocos2d::Director::getInstance()->replaceScene(scene);
		scene->CheckUpdate(coldUpdate, hotfix);
	}

	void ClientLogin::StartUniverseHotfix()
	{
		GetClientInfo()->SetAppState(APPSTATE_HOTFIXING);
		GetInputManager().RegisterInputHandler(GetGameUIPtr());
		GetGameUIPtr()->SetOldUIEnable(false);
		////打开热更新的界面在这里写
		auto scene = HotFixScene::create();
		cocos2d::Director::getInstance()->replaceScene(scene);
		scene->StartHotfix();
	}

	void ClientLogin::PostHotfix(bool isImmediately)
	{
		/*
		* 这里找一个时机，热更场景的内容先释放，因为有跟登录等共用的package，后释放会影响其他地方使用
		*/
		auto scene = cocos2d::Scene::create();
		cocos2d::Director::getInstance()->replaceScene(scene);

#ifdef IWORLD_UNIVERSE_BUILD
		Mini::GetHttpReportMgr().reportOverseaEngineInit();
#endif
		GetIWorldConfig().InitSoundSystem();
		GetInputManager().UnregisterInputHandler(GetGameUIPtr());
		GetGameUIPtr()->SetOldUIEnable(true);
		ReloadShaderMaterial();

		UIRenderer::GetInstance().PostHotfix();

		//初始化lua虚拟机
		ScriptVM::game();
		//开启调试，lua的调试配置在此
#ifdef IWORLD_SERVER_BUILD		
	    const char *print_log = GetClientInfoProxy()->getEnterParam("print_all_log");
		if (print_log && print_log[0] != '\0')
		{
		    GetDebugMgr().RentServerSetShowLog(true);
	    }
#endif		
		GetDebugMgr().StartGame();
#ifndef IWORLD_SERVER_BUILD
		//资源路径---LL：********
		MultiResPath::setSubproject(Rainbow::GetGameSetting().m_CurSubprojectInfo.name.c_str());
		fairygui::UIPackage::setSubprojectName(Rainbow::GetGameSetting().m_CurSubprojectInfo.name.c_str());
		GetClientInfo()->readUIThemeConfig();
#endif
		fairygui::UISkinColorManager::getInstance()->parseSkinColorConfigFile("miniui/miniworld/skinTextColor.json");
		GetClientInfo()->setSandboxEngineState(GAME_3DENGINE_LOADING);
		core::string netstate = IntToString(GetClientInfo()->getNetworkState());
		STATISTICS_INTERFACE_EXEC(gameEvent("StartEvent", "NetState", netstate.c_str()), 0);

#ifdef IWORLD_UNIVERSE_BUILD
		Mini::GetHttpReportMgr().reportOverseaEngineFinish();
#endif
		//这里已经开始初始化lua了
		if (!this->InitGameLuaEnv()) return;

		AdventureGuideMgr::GetInstancePtr();

		//获取设备机型的devicemodel
		auto gameSettingMgr = Rainbow::Setting::GameConfigMgr::GetInstance();
		gameSettingMgr->SetDeviceQualityValue(gameSettingMgr->GetSettingQualityValueByDevice());

		//这里面会设置runting的状态
		this->StartSandbox();

		//android和ios的sandbox lib同步了，再打开注释
		GetClientGameManagerPtr()->createNetHandlerRegister();
		GetGameRuntimePtr()->Init();
#pragma region PluginConfigRegion
		GetPluginManagerPtr()->Init();
		//SandBoxManager::getSingletonPtr()->subscibe_(static_cast<IEventExcute*>(ClientManager::getInstance()), SandBoxMgrEventID::EVENT_CLIENT_MANAGER_MINI_INTERVAL, 0, 0, "mini_interval");
#pragma endregion

		SandboxRuntime::getSingleton().Init();

#if OGRE_PLATFORM == OGRE_PLATFORM_APPLE || OGRE_PLATFORM == OGRE_PLATFORM_ANDROID
		OnInitGameDataFinished();
#endif
#ifndef IWORLD_SERVER_BUILD
		//------引擎代码初始化好，告知lua
		ScriptVM::game()->callString("EngineInited()");
#endif //IWORLD_SERVER_BUILD
		if (isImmediately)
		{
    #if PLATFORM_ANDROID
			WarningStringMsg("[AppUpdate]PostHotfix removeSplashView start");
			GameBaseActivity::removeSplashView();
			WarningStringMsg("[AppUpdate]PostHotfix removeSplashView end");
    #endif
		}

#if PLATFORM_IOS || PLATFORM_ANDROID || PLATFORM_OHOS
		MINIW::CheckDeviceRegisterInfo();
#endif

		if (MNSandbox::SandboxCoreDriver::GetInstancePtr())
		{
			MNSandbox::SandboxEventDispatcherManager::GetGlobalInstance().Emit("GE_IP_COUNTRY_FRESH", MNSandbox::SandboxContext(nullptr));
		}
		cocos2d::Director::getInstance()->setStatusLabelUpdated(true);
#if WATER_MARK && !DEDICATED_SERVER && !BUILD_MINI_EDITOR_APP
#if !DEDICATED_SERVER
	SetDevelopmentVersion();
#endif
#else

	#if IWORLD_DEV_BUILD && !DEDICATED_SERVER
			bool need = true;
			ScriptVM::game()->callFunction("ClientNeedDevelopmentMask", ">b", &need);
			if (need)
			{
				//初始化水印
				SetDevelopmentVersion();
			}
	#endif
#endif
#ifdef IWORLD_UNIVERSE_BUILD
		//重置功能配置
		IModuleConf::onPostHotfix();
#endif
	}

	void ClientLogin::InitOsEnvInfo()
	{
		std::string country = GetOsCountry();
		std::string osCountry = GameZoneCsv::getInstance()->getCountryCode(&country);

		gFunc_setCountryOs(osCountry);
		gFunc_setLanguageOs(GetOsLanguage());
	}

	bool ClientLogin::InitSubproject()
	{
		bool load_ret = true;
		//{1}语言表
		load_ret &= GameLanguageCsv::getInstance()->load();
		//--记录语言表的加载状态 --20220810
		GetClientInfo()->setLanguageCfgLoadRet(load_ret);

		//{2}国家大区配置表
		load_ret &= GameZoneCsv::getInstance()->load();

		//{3}初始化子项目(资源解耦&开关项)
		IModuleConf::loadModule(Rainbow::GetGameSetting().m_CurSubprojectInfo.name.c_str());
		//{4}加载本地化配置
		MultiLocalMgr::getSingleton()->loadConf();

		return load_ret;
	}

	void ClientLogin::PreLoadFirstConfig()
	{
		bool load_ret = true;
		//{1}语言表
		load_ret &= GameLanguageCsv::getInstance()->load();
		//--记录语言表的加载状态
		GetClientInfo()->setLanguageCfgLoadRet(load_ret);
	}


	void ClientLogin::InitUserTypePointer(MINIW::ScriptVM* scriptVM)
	{
		if (!scriptVM)
		{
			ErrorStringMsg("ScriptVM::game Get Error");
			return;
		}
		//scriptVM.setUserTypePointer("ClientMgr", "ClientManager", this);
		//scriptVM->setUserTypePointer("ClientLogin", "ClientLogin", this);
		scriptVM->setUserTypePointer("SandboxRuntime", "SandboxRuntime", SandboxRuntime::getSingletonPtr());
		scriptVM->setUserTypePointer("PlatformUtility", "PlatformUtility", PlatformUtility::GetInstancePtr());
		scriptVM->setUserTypePointer("CommonUtil", "CommonUtil", CommonUtil::GetInstancePtr());
#ifdef MODULE_FUNCTION_ENABLE_STATISTICS
		GetStatisticRainforestPtr();
		StatisticTerrgen::getInstance();
		scriptVM->setUserTypePointer("StatisticsTools", "StatisticsTools", StatisticsTools::getInstance());
#endif
		scriptVM->setUserTypePointer("LuaInterface", "LuaInterface", GetLuaInterfacePtr());
	}

	bool ClientLogin::InitGameLuaEnv()
	{
		ScriptVM* scriptVM = ScriptVM::game();

		lua_State* luaState = scriptVM->getLuaState();
		//增加Game需要特殊处理的全局函数
		{
			Rainbow::MiniLua::LuaGuard g(luaState);
			luaL_register(luaState, "_G", game_global_funcs);

		}
		MNSandbox::Core::Create(scriptVM);
		//将4399渠道需要的AntiaddictionMgr的事件注册操作 移动到MNSandbox::Core::Create 之后
		//事件系统需要先Create  
#pragma region InitAntiaddictionMgr
		GetClientInfo()->InitAntiaddictionMgr();
#pragma endregion

		GetKeywordFilterMgrPtr();
		//fairygui::UIConfig::registerFont(fairygui::UIConfig::defaultFont, Rainbow::GetRainbowDefaultFont());
		
		{
			//MultiLocalMgr::getSingleton()->InitFontsCfg();
			//本地化字体初始化
			if (!MultiLocalMgr::getSingleton()->FontsCfgIsEmpty())
			{
				std::vector<std::string>& fontsPath = MultiLocalMgr::getSingleton()->GetFontsPath();
				////两套UI 所以需要设置两次
				//UIRenderer::GetInstance().SetMultiLocalFontsPath(fontsPath);
				//新UI这次的设置会更新引擎内部字体缓存
				cocos2d::Director::getInstance()->setMultiLocalFontsPath(fontsPath);
				//存在多语言,需要将海外优秀级最高的字体设置为默认字体,防止因为引擎默认字体缺失,出现底层GUI文字失效的问题
				Rainbow::SetRainbowDefaultFont(fontsPath[0].c_str());
			}
		}

		tolua_open_all(luaState);

		scriptVM->OpenToluaUI();

		InitUserTypePointer(scriptVM);

		registerCustomView();

		ClientApp& app = GetClientApp();
#ifdef IWORLD_SERVER_BUILD
		if (strcmp(GetClientInfo()->getEnterParam("rent_lua_log"), "1") == 0)
		{
			LOG_INFO("lua log open\n");
			GetClientInfo()->setRentLuaLogFlag(1);    //开始打印日志
		}
		else
		{
			LOG_INFO("lua log closed\n");
		}
#ifdef WINDOWS_SERVER
		scriptVM->callFile("luascript/debugscript_init.lua");
#endif
		scriptVM->callFile("luascript/scriptserver_init.lua");
#else
		scriptVM->callFile("luascript/script_init.lua");
#endif

		UInt64 curTime = GetTimeMS();
		scriptVM->callFunction("__init__", "ii", curTime / 1000, curTime % 1000);
		MNSandbox::Core::Init();
		MNSandbox::NodeRegister::PrintAllNodesEnd();

		scriptVM->callFile("sandboxengine/toc/sandbox_init.lua");
		scriptVM->callFile("sandboxengine/toc/sandboxcore_init.lua");

#pragma region PluginConfigRegion
		IWorldConfig::getInstance()->CreatePlugin(scriptVM);
		//GetPluginManagerPtr()->Init();
#pragma endregion

		scriptVM->setUserTypePointer("SandboxMgr", "SandBoxManager", GetSandBoxManagerPtr());
		scriptVM->setUserTypePointer("SandboxUtilsMgr", "SandBoxUtilsManager", GetSandBoxUtilsManagerPtr());
		scriptVM->callFile("sandboxengine/toc/gameplay_init.lua");
		scriptVM->callFile("sandboxengine/toc/gameAI_init.lua");

		CameraManager::GetInstance().initEventCallBack();

#ifdef DEDICATED_SERVER

#ifndef STUDIO_SERVER
		InitZmqMgr();
#endif
		StandaloneServer::subscribeEvents();
#endif
		GetGameRuntime().InitGameData();

		return true;
	}

	void ClientLogin::StartSandbox()
	{
		char lang[8];
		sprintf(lang, "%d", GetIWorldConfig().getGameData("lang"));
		const std::string& deviceId = GetClientInfo()->getDeviceID();
		STATISTICS_INTERFACE_EXEC(gameAction(StatisticsActionId::SAID_GAMELOAD2, 3, deviceId.c_str(), m_bFirstEnter ? "1" : "2", lang), 0);

		ScriptVM* scriptVM = ScriptVM::game();
		//加载结束，开启内网验证
#if defined(IWORLD_DEV_BUILD ) && defined(IWORLD_LOGIN_CHECK)
		if (GetIWorldConfigPtr()->getGameData("game_env") != 1)
		{
			//if (g_pFrameMgr) g_pFrameMgr->setFuncF4Enable(false);
			if (GameUI::GetFrameManagerPtr())
				GameUI::GetFrameManagerPtr()->setFuncF4Enable(false); //先禁用F4功能

			bool callSucc = false; //是否调用成功并弹出界面
			//MINIW::DataStream* fp = MINIW::FileManager::getSingletonPtr()->openFile("ui/mobile/logincheck.lua", true);
			AutoRefPtr<Rainbow::DataStream> fp = GetFileManager().OpenFile("ui/mobile/logincheck.lua", true);
			if (fp != NULL)
			{
				string conten = b64_decode_my((const char*)fp->GetMemoryImage(), fp->Size());
				if (scriptVM->callString(conten.c_str()))
				{
					callSucc = scriptVM->callFunction("OursValidateFrameInit", "");
				}
			}
			if (!callSucc) //调用失败
			{
				////MessageBox(NULL, _T("Lua文件缺失或已被篡改！"), _T("出错了"), MB_OK);
				GetClientApp().GameExit(true);//gameExit(true);
			}
		}
#endif

		//#ifndef IWORLD_SERVER_BUILD
		//		RenderSystem::getSingleton().showCursor(true);
		//#endif

#ifdef IWORLD_SERVER_BUILD

		WarningStringMsg("[server]ClientLogin::StartSandbox");

		GetClientAccountMgr().requestEnterGame2New();
		if (BootConfig::HasKey("rent_config")) {
			//租赁服
			WarningStringMsg("[server]RentRoomInit %s", BootConfig::GetValue("rent_config"));
			int ret_ = 0;
			scriptVM->callFunction("RentRoomInit", ">i", &ret_);
			if (ret_ == 0) {
				GetClientAccountMgr().enterGame();
			}
#ifdef WINDOWS_SERVER
			else { MINIW::PopMessageBox("[server]RentRoomInit error!!", "server error"); }
#endif
		}
		else if (BootConfig::HasKey("account") && BootConfig::HasKey("toloadmapid")) {
			//房间服
			WarningStringMsg("[server]ClientLogin::StartSandbox find account toloadmapid");
			GetClientAccountMgr().enterGame();
		}
		else {
			LOG_INFO("startRunning kvhash error ");
		}


#else
#ifdef IWORLD_TARGET_LIB
		/*
		m_GameUI->Update(0);
		LoadStepCounter loadcounter;
		loadcounter.reset(0, 10, 10);
		while(loadcounter.getProgress() < 100)
		{
			initGameDataStep(loadcounter);
		}

		startDirectGame();
		*/

		GetClientGameManagerPtr()->gotoGame("SimpleLoadingStage");
		if (GetInputManagerPtr() && GetClientGameManagerPtr()->getLoadingGame())
			GetInputManagerPtr()->RegisterInputHandler(GetClientGameManagerPtr()->getLoadingGame());
#else
		GetClientGameManagerPtr()->gotoGame("MainMenuStage");

		if (GetInputManagerPtr() && GetClientGameManagerPtr()->getLoadingGame())
			GetInputManagerPtr()->RegisterInputHandler(GetClientGameManagerPtr()->getLoadingGame());
#endif //IWORLD_TARGET_LIB
#endif //IWORLD_SERVER_BUILD
		static int enter_count = 0;
		/*if (m_isNew4399SDK)
		{
			Login4399NewSDK();
		}*/
		GetClientInfo()->doLogin4399NewSDK();

		/*m_CurTick = Timer::getSystemTick();
		m_LogicalClock = ENG_NEW(Clock(m_CurTick));
		m_GameTickAccum = 0;*/
		GetClientInfo()->SetAppState(APPSTATE_RUNNING);
		MNSandbox::Core::OpenSandbox();
		
		//initSuccess();
		OnStartSandbox();
	}

	void ClientLogin::InitGameDataStep(LoadStepCounter& loadcounter)
	{

#ifdef _WIN32
		if (GetIWorldConfig().getGameData("debug_ui") == 1) {
			//不加载任何界面XML和LUA，按F12去后发加载，按F11显示，测试单个UI使用
			ScriptVM::game()->callString("g_debug_ui=true;");
			loadcounter.setProgressFull();
			return;
		}
#endif
		unsigned int t1 = Timer::getSystemTick();

		if (loadcounter.getStage() == 0)
		{
			//cocos2d::Director::getInstance()->restart();
		//增加了加载CSV表格和xml中lua文件加载的步骤 code_by:huangfubin
			int stagecounter = loadcounter.step();
			//1-4空几帧，防止app anr
			if (stagecounter > 4)
			{
				g_DefMgr.loadInLoading(stagecounter - 4); //分步加载csv
			}

			if (stagecounter == 25)
			{
				ScriptVM::game()->callString("GetInst('UIManager'):InitXmlPathList()");
			}

			if (stagecounter == 27)
			{
#if MINI_NEW_UI
				ScriptVM::game()->callFile("miniui/miniui_init.lua");
#endif
			}

			if (loadcounter.stageCompleted())
			{
				SomeInitAfterCsvLoad();
				loadcounter.gotoStage(1, 10, 30);
			}
		}
		else if (loadcounter.getStage() == 1)
		{
			int stagecounter = loadcounter.step();
			//1-4空几帧，防止app anr
			if (stagecounter == 4) ModEditorManager::GetInstance().load();
			else if (stagecounter == 5) ModFileManager::GetInstance().checkInitialMod("data/mods");
			else if (stagecounter == 6) ModFileManager::GetInstance().refreshAllModsPath("data/mods");
			else if (stagecounter == 7) ModManager::GetInstance().init();
			else if (stagecounter == 8) CheckMainDataFiles();
			else if (stagecounter == 10)
			{
				//加载toc的文件列表
				const char* uitoc = "ui/mobile/game_main_init.lua";
				bool isDynamicLoad = false;
				ScriptVM::game()->callFunction("GetEnableDynamicLoadFile", ">b", &isDynamicLoad);
				if (isDynamicLoad)
				{
					uitoc = "ui/mobile/game_enterLobby_init.lua";

					if (GetClientInfo()->getIsOverseasVer())
					{
						if (GetClientInfo()->getIsOverseasGrayVer())
						{
							uitoc = "overseas_gray_res/ui/mobile/game_enterLobby_init.lua";
						}
						else
						{
							uitoc = "overseas_res/ui/mobile/game_enterLobby_init.lua";
						}
					}
				}
				int nfiles = GetGameUIPtr()->readTOCList(uitoc);
				if (nfiles < 0) nfiles = 0;
				loadcounter.gotoStage(2, nfiles, 30);
			}
		}
		else if (loadcounter.getStage() == 2) //分步加载单个toc中配置的xml和lua文件
		{
			int fileindex = loadcounter.step() - 1;
			if (fileindex >= 0) GetGameUIPtr()->parseSingleTOCFile(fileindex);

			if (loadcounter.stageCompleted())
			{
				//char lang[8];
				//sprintf(lang, "%d", GetIWorldConfigPtr()->getGameData("lang"));
				//STATISTICS_INTERFACE_EXEC(gameAction(StatisticsActionId::SAID_GAMELOAD3,3,getDeviceID().c_str(),isFirstEnterGame()?"1":"2",lang), 0);
				STATISTICS_INTERFACE_EXEC(StatisticsGameLoadAction(StatisticsActionId::SAID_GAMELOAD3), 0);
				loadcounter.gotoStage(3, GetBlockMaterialMgr().getInitStepCount(), 10);
			}
		}
		else if (loadcounter.getStage() == 3)
		{
			BlockMaterialMgr* blockMtlMgr = &GetBlockMaterialMgr();
			if (blockMtlMgr->m_initStep == -1) {
				int step = loadcounter.step();
				blockMtlMgr->initStep(step);//RenderSystem 渲染相关要在主线程
			}
			else if (blockMtlMgr->m_initStep == 1) {
				blockMtlMgr->initStep(loadcounter.step());
			}
			else if (blockMtlMgr->m_initStep >= 3) {
				blockMtlMgr->initStep(loadcounter.step());
			}
		}
	}

	void ClientLogin::SomeInitAfterCsvLoad()
	{
		//GetCraftMgr();
		//GetBluePrintMgr();
	}

	void ClientLogin::CheckMainDataFiles()
	{
		//抽取maindata文件,先放一个版本maindata到安装包里,减少后续版本cdn的下载
#ifndef _WIN32
		//UNDONE 其他平台需要补充这块的逻辑 by bobu
		/*do
		{
			if (FileManager::getSingleton().isStdioDirExist("maindata") && !FileManager::getSingleton().isStdioFileExist("maindata.zip"))
			{
				break;
			}

			std::string stdiopath;
			std::string stdiodir;
			if (!FileManager::getSingleton().isFileExistLocal("maindata.zip"))
			{
				break;
			}

			if (!FileManager::getSingleton().copyFileFromPkgToStdio("maindata.zip", "maindata.zip"))
			{
				OnStatisticsGameEvent("uncompress_maindata", "failed1");
				break;
			}

			FileManager::getSingleton().gamePath2StdioPath(stdiopath, "maindata.zip");
			FileManager::getSingleton().gamePath2StdioPath(stdiodir, "");

			bool has_error = (CompressTool::uncompressZip(stdiopath.c_str(), stdiodir.c_str()) != 0);
			if (has_error)
			{
				OnStatisticsGameEvent("uncompress_maindata", "failed2");
				break;
			}
			FileManager::getSingleton().deleteStdioFile("maindata.zip");
			OnStatisticsGameEvent("uncompress_maindata", "ok");
		} while (0);*/
#endif
	}

	void ClientLogin::InitPayLoadParam()
	{
		MINIW::MiniMutualParams::getInstance()->RefreshPayLoadParam();
	}

	/*------------------------------
	* 加载PKG列表
	* @return@ 加载不成功不允许启动
	*/
	bool ClientLogin::LoadNativePkg()
	{
		//bool unified = HotFixServiceUnified::IsUnifiedPkg();
		bool loaded = loadNativePkg();
		//加载成功
		if (loaded)
			return true;

		WarningStringMsg("[basehotfix]--rvLog ClientLogin::LoadNativePkg loaded:%d ", loaded);
		GetClientInfo()->SetAppState(APPSTATE_HOTFIXING);
		GetInputManager().RegisterInputHandler(GetGameUIPtr());
		GetGameUIPtr()->SetOldUIEnable(false);
#if !RAINBOW_EDITOR
		//初始化更新场景
		auto scene = BaseAppUpdateScene::create();
		cocos2d::Director::getInstance()->replaceScene(scene);
		//启动提示
		scene->NativeCaveat(loaded);
#endif

		return false;
	}

	/*------------------------------
	* 加载PKG版本号
	* @base = 基础包
	* @patch= 补丁包
	* @return@ false=缺少pkg资源包
	*/
	bool ClientLogin::loadNativePkg(bool base, bool patch)
	{
		if (!base && !patch)
			return false;

		bool baseAll = true;	//全部基础包
		dynamic_array<GamePackageInfo>& infoList = GetGameSetting().m_PkgLists;
		for (int i = 0; i < infoList.size(); i++)
		{
			GamePackageInfo& info = infoList[i];
			bool isLuajitMeet = true;
#if PLATFORM_ARCH_32
			int luajitVersion = 32;
#else
			int luajitVersion = 64;
#endif

			if (info.luajitVersion != 0 && info.luajitVersion != luajitVersion)
			{
				isLuajitMeet = false;
			}
			if ((info.renderer != kGfxRendererUnknown) && (info.renderer != Rainbow::GetGfxDeviceSetting().GetGfxDeviceRenderer()) || !isLuajitMeet)
			{
				GetGameSetting().SetPkgVersion(i + 1, 0);
				WarningStringMsg("[basehotfix] not suport pkg:%s,version:%d", info.pkgFilePath.c_str(), 0);
				continue;
			}

			FilePkgBase* basePkg = nullptr;
			FilePkgBase* patchPkg = nullptr;
			core::string pkgName = HotfixLocalUnit::FileNamePKG(info.name);
			core::string patchName = HotfixLocalUnit::FileNamePKGPatch(info.name);

			//加载基础包
			if (base)
			{
				//存在先卸载
				GetFileManager().RemovePackage(pkgName.c_str());

#if PLATFORM_OHOS
                core::string filePath = GetFileManager().IsFileExistWritePath(info.pkgFilePath.c_str())
                    ? FileUtils::GetWritableFilesPath() + info.pkgFilePath
                    : FileUtils::GetReadOnlyResourcePath() + info.pkgFilePath;
#else
				//安装文件
				core::string filePath = HotfixLocalUnit::JoinPkgPathInstall(info.pkgFilePath); // 'assets/' + pkgFilePath
#endif

				if (info.pkgFilePath.starts_with('/')) // sdcard 绝对路径
				{
					filePath = info.pkgFilePath;
					basePkg = GetFileManager().AddPackage(Rainbow::FILEPKG_DIR, pkgName.c_str(), filePath.c_str(), info.priority, FILETYPE_SERIALIZE_FILE);
				}
				else
				{
					basePkg = GetFileManager().AddPackage(Rainbow::FILEPKG_ASSET, pkgName.c_str(), filePath.c_str(), info.priority, FILETYPE_SERIALIZE_FILE);
				}
				if (nullptr != basePkg)
					basePkg->SetFilePrefix(info.filePrefix);
				else
					baseAll = false;
			}
			else
			{
				basePkg = GetFileManager().FindPackage(pkgName.c_str()).Get();
			}
			//加载补丁包
			if (patch)
			{
				//存在先卸载
				GetFileManager().RemovePackage(patchName.c_str());

				//安装文件
				core::string filePath = HotfixLocalUnit::JoinPkgPathPatch(patchName);
				patchPkg = GetFileManager().AddPackage(Rainbow::FILEPKG_ASSET, patchName.c_str(), filePath.c_str(), info.priority + 100, FILETYPE_SERIALIZE_FILE);
				if (nullptr != patchPkg)
					patchPkg->SetFilePrefix(info.filePrefix);
			}
			else
			{
				patchPkg = GetFileManager().FindPackage(patchName.c_str()).Get();
			}

			//版本号处理
			PackageAsset* baseAsset = static_cast<PackageAsset*>(basePkg);
			PackageAsset* patchAsset = static_cast<PackageAsset*>(patchPkg);
			int baseVer = (nullptr != baseAsset) ? baseAsset->GetResVersion() : 0;
			int patchVer = (nullptr != patchAsset) ? patchAsset->GetResVersion() : 0;
			//显示版本号
			int resVer = (baseVer >= patchVer) ? baseVer : patchVer;
			//把最新的版本号设置进去
			GetGameSetting().SetPkgVersion(i + 1, resVer);
			WarningStringMsg("[basehotfix] pkg:%s,version:base(%d) patch(%d)", info.pkgFilePath.c_str(), baseVer, patchVer);
		}

		return baseAll;
	}

	void ClientLogin::StartDnsConvert()
	{
		GetClientInfo()->SetAppState(APPSTATE_DNS_CONVERT);
		GetInputManager().RegisterInputHandler(GetGameUIPtr());
		GetGameUIPtr()->SetOldUIEnable(false);
		auto scene = DnsConvertScene::create();
		cocos2d::Director::getInstance()->replaceScene(scene);
		scene->StartDnsConvert();
	}
}
