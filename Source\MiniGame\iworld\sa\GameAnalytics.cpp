#include "GameAnalytics.h"

#include <iostream>
#include <type_traits>
#include "GetClientInfo.h"
#include "IWorldConfig.h"
#include "Platforms/PlatformInterface.h"

using namespace thinkingdata;

// 全局变量定义
GameAnalytics* g_pGameAnalytics = nullptr;

GameAnalytics::CommonProperties GameAnalytics::s_commonProps;
bool GameAnalytics::m_initialized = false;

// 构造函数和析构函数实现
GameAnalytics::GameAnalytics() {
  // 构造函数实现
}

GameAnalytics::~GameAnalytics() {
  // 析构函数实现
}

// 初始化方法实现
bool GameAnalytics::Init(const std::string& device_id, int env) {
  // 如果全局变量已经初始化，直接返回true
  if (g_pGameAnalytics != nullptr) {
    return true;
  }
  
  // 创建全局对象
  g_pGameAnalytics = new GameAnalytics();
  
  // 初始化埋点SDK
  std::string appid = "a12c62532cf54941ba8cb3cb63784b07"; // 数据文件路径
  std::string server_url = "https://tga.mini1.cn";
  bool is_login_id = false; // device_id 不是登录ID
  int max_staging_record_count = 10000; // 最大暂存记录数

  // 配置ThinkingData SDK
  TDConfig td_config;
  td_config.appid = appid;
  td_config.server_url = server_url;
  td_config.enableAutoCalibrated = true; // 自动时间校准
  td_config.mode = TDMode::TD_NORMAL;
  td_config.databaseLimit = max_staging_record_count;
  td_config.dataExpression = 15;

  bool success = ThinkingAnalyticsAPI::Init(td_config);
  
  if (success) {
    m_initialized = true;
    s_commonProps.env = env;                // 设置环境
    s_commonProps.device_id = device_id;    // 设置设备ID
    s_commonProps.log_id = genLogid();
    // 可以在这里设置其他默认属性
  }

  ThinkingAnalyticsAPI::EnableLog(true);
  
  return success;
}


void GameAnalytics::InitCommonProps(){

    MINIW::ClientInfo* clientinfo  = MINIW::GetClientInfo();

    int game_env = GetIWorldConfig().getGameData("game_env");
    const std::string  app_version = clientinfo->GetClientVersionStr();
    int apiid = clientinfo->GetAppId();
    GameAnalytics::SetGameEvn(apiid, app_version,game_env);
    int64_t session_start_time = Rainbow::GetTimeSec();
    std::string session_id = GetIWorldConfigProxy()->getSession_id();
    GameAnalytics::SetSessionInfo(session_id, session_start_time);

    const std::string country = MINIW::GetSharedCountry();
    const std::string province = "";
    GameAnalytics::SetCountryInfo(country,province);

    const std::string ip_address = MINIW::fetchLaunchIp();
    int apn = clientinfo->getNetworkState();
    const std::string os_type = GetClientInfoProxy()->getPlatformStr();
    GameAnalytics::SetDeviceInfo(ip_address,os_type, apn);

    int channel_id = clientinfo->getChannelId();
    GameAnalytics::SetChannelInfo(channel_id);

    s_commonProps.log_id = session_id;

}

// 设置游戏环境
void GameAnalytics::SetGameEvn(int apiid, const std::string& app_ver, int env) {
    s_commonProps.apiid = apiid;
    s_commonProps.app_version = app_ver;
    s_commonProps.env = env;
}

// 设置会话相关信息
void GameAnalytics::SetSessionInfo(const std::string& session_id, int64_t session_start_time) {
    s_commonProps.session_id = session_id;
    s_commonProps.session_start_time = session_start_time;
}

// 设置游戏会话信息
void GameAnalytics::SetGameSessionInfo(const std::string& game_session_id, int64_t game_session_start_time) {
    s_commonProps.game_session_id = game_session_id;
    s_commonProps.game_session_start_time = game_session_start_time;
}

// 设置国家信息
void GameAnalytics::SetCountryInfo(const std::string& country, std::string province) {
    s_commonProps.country = country;
    s_commonProps.province = province;
}

// 设置设备信息
void GameAnalytics::SetDeviceInfo(const std::string& ip_address, const std::string& os_type, int apn) {
    s_commonProps.ip_address = ip_address;
    s_commonProps.apn = apn;
    s_commonProps.os_type = os_type;
}

// 设置渠道信息
void GameAnalytics::SetChannelInfo(int channel_id) {
    s_commonProps.channel = channel_id;
}

void GameAnalytics::Login(const std::string& login_id) {
    ThinkingAnalyticsAPI::Login(login_id);
    s_commonProps.uin = login_id;


}

void GameAnalytics::Logout() {
    m_initialized = false;
    // 调用SDK登出
    ThinkingAnalyticsAPI::LogOut();
}


void GameAnalytics::TrackEvent(const std::string& event_name){
    if (!m_initialized) {
        std::cout << "[GameAnalytics] Not initialized" << std::endl;
        return;
    }
    if (event_name.empty()) {
        return;
    }
    thinkingdata::TDJSONObject properties = createCommonProperties();
    ThinkingAnalyticsAPI::Track(event_name, properties);
    ThinkingAnalyticsAPI::Flush();
}

void GameAnalytics::TrackEvent(const std::string& event_name, std::map<std::string, Value> data){
    if (!m_initialized) {
        std::cout << "[GameAnalytics] Not initialized" << std::endl;
        return;
    }

    if (event_name.empty()) {
        return;
    }
    
    thinkingdata::TDJSONObject properties = createCommonProperties();
    
    for (auto it = data.begin(); it != data.end(); it++) {
        std::string key = it->first;
        Value value = it->second;
        
        switch (value.type) {
            case Value::Type::String:
                properties.SetString(key, value.string_value);
                break;
            case Value::Type::Int:
                properties.SetNumber(key, value.int_value);
                break;
            case Value::Type::Float:
                properties.SetNumber(key, value.float_value);
                break;
            case Value::Type::Bool:
                properties.SetBool(key, value.bool_value);
                break;
        }
    }
    
    ThinkingAnalyticsAPI::Track(event_name, properties);
    ThinkingAnalyticsAPI::Flush();
}

void GameAnalytics::TrackPlayerCreated(const std::string& character_id, const std::string& character_class) {
    thinkingdata::TDJSONObject properties = createCommonProperties();
    properties.SetString("character_id", character_id);
    properties.SetString("character_class", character_class);
    ThinkingAnalyticsAPI::Track("PlayerCreated", properties);
}

void GameAnalytics::TrackPlayerLoaded(const std::string& character_id) {
    thinkingdata::TDJSONObject properties = createCommonProperties();
    properties.SetString("character_id", character_id);
    ThinkingAnalyticsAPI::Track("PlayerLoaded", properties);
}

thinkingdata::TDJSONObject GameAnalytics::createCommonProperties() {
    thinkingdata::TDJSONObject properties;

    properties.SetString("session_id", s_commonProps.session_id);
    properties.SetNumber("session_start_time", s_commonProps.session_start_time);
    
    properties.SetNumber("env", s_commonProps.env);
    properties.SetString("ip_address", s_commonProps.ip_address);
    properties.SetNumber("apn", s_commonProps.apn);
    properties.SetString("app_version", s_commonProps.app_version);
    properties.SetNumber("apiid", s_commonProps.apiid);
    properties.SetNumber("channel", s_commonProps.channel);
    properties.SetString("os_type", s_commonProps.os_type);
    properties.SetString("country", s_commonProps.country);
    properties.SetString("province", s_commonProps.province);
    
    properties.SetString("device_id", s_commonProps.device_id);
    properties.SetString("uin", s_commonProps.uin);
    properties.SetString("log_id", s_commonProps.log_id);
    
    properties.SetString("game_session_id", s_commonProps.game_session_id);
    properties.SetNumber("game_session_start_time", s_commonProps.game_session_start_time);
 
    return properties;
}


// 模板函数实现
template<typename T>
void GameAnalytics::SetUserProfile(const std::string& property_name, const T& value) {
    TDJSONObject userProps;
    if constexpr (std::is_same_v<T, std::string>) {
        userProps.SetString(property_name, value);
    } else if constexpr (std::is_same_v<T, int>) {
        userProps.SetNumber(property_name, value);
    } else if constexpr (std::is_same_v<T, bool>) {
        userProps.SetBool(property_name, value);
    } else if constexpr (std::is_same_v<T, double> || std::is_same_v<T, float>) {
        userProps.SetNumber(property_name, static_cast<double>(value));
    }
    ThinkingAnalyticsAPI::UserSet(userProps);
}

// 显式实例化常用类型
template void GameAnalytics::SetUserProfile<std::string>(const std::string&, const std::string&);
template void GameAnalytics::SetUserProfile<int>(const std::string&, const int&);
template void GameAnalytics::SetUserProfile<bool>(const std::string&, const bool&);
template void GameAnalytics::SetUserProfile<double>(const std::string&, const double&);
template void GameAnalytics::SetUserProfile<float>(const std::string&, const float&);


std::string GameAnalytics::genLogid() {
    return "";
}

/*
使用示例：

// 创建数据map
std::map<std::string, GameAnalyticsDataValue> eventData;

// 添加不同类型的数据
eventData["player_level"] = GameAnalyticsDataValue(25);           // int
eventData["score"] = GameAnalyticsDataValue(1234.5f);           // float
eventData["is_vip"] = GameAnalyticsDataValue(true);             // bool
eventData["player_name"] = GameAnalyticsDataValue("张三");       // string
eventData["map_name"] = GameAnalyticsDataValue("forest_level"); // string

// 发送事件
GameAnalytics::TrackEvent("level_complete", eventData);

*/