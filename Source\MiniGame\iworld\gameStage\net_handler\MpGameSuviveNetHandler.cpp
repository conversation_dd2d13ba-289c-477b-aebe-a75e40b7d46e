#include "MpGameSuviveNetHandler.h"
#include "backpack.h"
#include "GameNetManager.h"
#include "SandBoxManager.h"
#include "ClientInfoProxy.h"
#include "ClientActor.h"
#include "MpGameSurvive.h"
#include "LegacyCompress.h"
#include "WorldManager.h"
#include "PlayerControl.h"
#include "ClientActorManager.h"
#include "File/DirVisitor.h"
#include "ClientPlayer.h"
#include "container_backpack.h"
#include "OgreStringUtil.h"
#include "OgreUtils.h"
#include "DebugDataMgr.h"
#include "PlayerControl.h"
#include "PlayerAttrib.h"
#include "OgreTimer.h"
#include "SandboxGFunc.h"
#include "GameInfoProxy.h"
#include "DefManagerProxy.h"
#include "File/FileManager.h"
#include "ICloudProxy.h"
#include "WorkbenchTechCsv.h"
#include "worldEvent/AirDrop/AirDropEvent.h"
#include "GameAnalytics.h"
#ifdef IWORLD_SERVER_BUILD
#include "DataHubService.h"
#include "proto_gs2ds.pb.h"


#endif

#include "ClientErrCode.h"
#include "Platforms/PlatformInterface.h"
#include "SandboxGlobalNotify.h"
#include "IPlayerCmdCommand.h"

#include "GameEvent.h"
#include "SandboxRemoteMsg.h"
#include "GetClientInfo.h"
#include "RoomManager.h"
#include "proto_pb/proto_ch.pb.h"
#include "MiniReportMgr.h"
#include "backpack/CraftingQueue.h"
#include "MiniReportMgr.h"
#include "json/jsonxx.h"
using namespace MINIW;
using namespace Rainbow;
using namespace MNSandbox;

// 联机模式下 客户端GM指令
bool ExecCmdOnClient(const char *cmd)
{
	core::string cmdstr(cmd);
	std::vector<core::string>paramsCore = Rainbow::StringUtil::split(cmdstr, " \t\n");
	std::vector<std::string>params;
	for (auto v : paramsCore)
	{
		params.push_back(v.c_str());
	}

	std::string name = params[0];
	std::transform(name.begin(), name.end(), name.begin(), ::toupper);

	// 部分由execCmd处理的指令
	if (name == "WALL")
	{
		g_pPlayerCtrl->execCmd(cmd);
		// 不要return 需要继续发给主机
	}
	else if (name == "ENABLEBOXDEBUG")
	{
		ClientActor::ms_enableDebugWrapBpx = (params.size() <= 1 || params[1] == "0");
		return true;
	}
	else if (name == "SLEEP")
	{
		if (params.size() > 1)
		{
			MINIW::ThreadSleep(Str2Int(params[1].c_str()) * 1000);
		}
		return true;
	}
	else if (name == "PHY")
	{
		DebugDataMgr::GetInstance().setEnablePHY(!DebugDataMgr::GetInstance().isEnablePHY());
		return true;
	}
	else if (name == "MOVESPEED")
	{
		if (g_pPlayerCtrl)
		{
			if (params.size() > 1)
			{
				float speed = Str2Float(params[1].c_str());
				if (speed < 0)
					speed = 0;

				speed = speed - 1;
				g_pPlayerCtrl->getPlayerAttrib()->setMoveSpeed(speed);
			}
		}
		return true;
	}
// 	else if (name == "GM")
// 	{
// #ifdef ENABLE_PLAYER_CMD_COMMAND
// 		if (g_pPlayerCtrl)
// 		{
// 			auto command = ENG_NEW(CallLuaGMFunctionCommand)();
// 			command->exec(*g_pPlayerCtrl, params);
// 		}
// #endif
//
// 		return true;
// 	}
	else if (name == "ENABLE_WBC_CLIENT=0" || name == "ENABLE_WBC_CLIENT=1")
	{
		Mini::GetHttpReportMgr().luaInterfaceReportStudio("/" + std::string(cmd));
		return true;
	}

	return false;
}


MpGameSurviveNetHandler::MpGameSurviveNetHandler()
{
	std::ostringstream ss;
	ss << MINIW::GetTimeStamp();
	m_lifeToken = ss.str();
}
MpGameSurviveNetHandler::~MpGameSurviveNetHandler()
{
}

void MpGameSurviveNetHandler::load()
{
	ClientGameNetHandler::load();
}

void MpGameSurviveNetHandler::registerNetHandler()
{
	registerHostNetHandler();
	registerClientNetHandler();
}

void MpGameSurviveNetHandler::registerHostNetHandler()
{
	REGIS_HOST_HANDLER(PB_NOTIFY_UPDATE_TOOL_MODEL_TEXTURE_CH, handleNotifyUpdateToolModelTexture2Host);
	REGIS_HOST_HANDLER(PB_VILLAGER_MODIFY_NAME_CH, handleVillagerModifyName2Host);
	REGIS_HOST_HANDLER(PB_PLAYER_NAVFINISHED_CH, handlePlayerNavFinished2Host);
	REGIS_HOST_HANDLER(PB_PLAYER_ATTR_SCALE_CH, handlePlayerAttrScale2Host);
	REGIS_HOST_HANDLER(PB_CUSTOM_MODEL_PRE_CH, handleCustomModelPre2Host);
	REGIS_HOST_HANDLER(PB_HEARTBEAT_CH, handleHeartBeat2Host);
	REGIS_HOST_HANDLER(PB_SYNC_CHUNK_DATA_CH, handleSyncChunkData2Host);
	REGIS_HOST_HANDLER(PB_ROLE_ENTER_WORLD_CH, handleRoleEnterWorld2Host);
	REGIS_HOST_HANDLER(PB_ROLE_LEAVE_WORLD_CH, handleRoleLeaveWorld2Host);
	REGIS_HOST_HANDLER(PB_ROLE_MOVE_CH, handleRoleMove2Host);
	REGIS_HOST_HANDLER(PB_ACTOR_REVIVE_CH, handleActorRevive2Host);
	REGIS_HOST_HANDLER(PB_ACTOR_GET_ACCOUNT_ITEM, handleActorGetAccountItem2Host);
	REGIS_HOST_HANDLER(PB_PLAYER_GOTOPOS_CH, handleRoleGotoPos2Host);
	REGIS_HOST_HANDLER(PB_BLOCK_INTERACT_CH, handleBlockInteract2Host);
	REGIS_HOST_HANDLER(PB_BLOCK_ATTACK_CH, handleAttackBlock2Host);
	REGIS_HOST_HANDLER(PB_BLOCK_PUNCH_CH, handleBlockPunch2Host);
	REGIS_HOST_HANDLER(PB_BLOCK_EXPLOIT_CH, handleBlockExploit2Host);
	REGIS_HOST_HANDLER(PB_ITEM_USE_CH, handleItemUse2Host);
	REGIS_HOST_HANDLER(PB_SET_HOOK_CH, handleSetHook2Host);
	REGIS_HOST_HANDLER(PB_ITEM_SKILL_USE_CH, handleItemSkillUse2Host);
	REGIS_HOST_HANDLER(PB_PLAYER_SPECIAL_SKILL_CH, handlePlayerSpecialSkill2Host);
	REGIS_HOST_HANDLER(PB_ALTAR_LUCKY_DRAW_CH, handlePlayerAltarLuckyDraw2Host);
	REGIS_HOST_HANDLER(PB_SPECIALITEM_USE_CH, handleSpecialItemUse2Host);
	REGIS_HOST_HANDLER(PB_ACTOR_INTERACT_CH, handleActorInteract2Host);
	REGIS_HOST_HANDLER(PB_RCLICKUP_INTERACT_CH, handleRClickUpInteract2Host);
	REGIS_HOST_HANDLER(PB_MOUSE_EVENT_CH, handlePCMouseEvent2Host);
	REGIS_HOST_HANDLER(PB_CRAFTING_QUEUE_ADD_TASK_CH, handleCraftQueueAddTask2Host);
	REGIS_HOST_HANDLER(PB_CRAFTING_QUEUE_REMOVE_TASK_CH, handleCraftQueueRemoveTask2Host);
	REGIS_HOST_HANDLER(PB_CRAFTING_QUEUE_SWAP_TASK_CH, handleCraftQueueSwapTask2Host);
	REGIS_HOST_HANDLER(PB_PLAYER_WAKE_UP_CH, handlePlayerWakeUp2Host);
	REGIS_HOST_HANDLER(PB_ACTOR_ANIM_CH, handleActorAnim2Host);
	REGIS_HOST_HANDLER(PB_ACCOUNT_HORSE_CH, handleAccountHorse2Host);
	REGIS_HOST_HANDLER(PB_BACKPACK_GRID_SWAP_CH, handleBackPackGridSwap2Host);
	REGIS_HOST_HANDLER(PB_BACKPACK_GRID_DISCARD_CH, handleBackPackGridDiscard2Host);
	REGIS_HOST_HANDLER(PB_BACKPACK_EQUIP_WEAPON_CH, handleBackPackGridEquipWeapon2Host);
	REGIS_HOST_HANDLER(PB_BACKPACK_SORT_CH, handleBackPackSort2Host);
	REGIS_HOST_HANDLER(PB_STORAGEBOX_SORT_CH, handleStorageBoxSort2Host);
	REGIS_HOST_HANDLER(PB_BACKPACK_SETITEM_CH, handleBackPackSetItem2Host);
	REGIS_HOST_HANDLER(PB_BACKPACK_SETITEMWITHOUTLIMIT_CH, handleBackPackSetItemWithoutLimit2Host);
	REGIS_HOST_HANDLER(PB_BACKPACK_MOVEITEM_CH, handleBackPackMoveItem2Host);
	REGIS_HOST_HANDLER(PB_BACKPACK_REMOVEITEM_CH, handleBackPackRemoveItem2Host);
	REGIS_HOST_HANDLER(PB_CRAFT_ITEM_CH, handleCraftItem2Host);
	REGIS_HOST_HANDLER(PB_ENCHANT_ITEM_CH, handleEnchantItem2Host);
	REGIS_HOST_HANDLER(PB_ENCHANT_ITEM_RANDOM_CH, handleEnchantItemRandom2Host);
	REGIS_HOST_HANDLER(PB_RUNE_OPERATE_CH, handleRuneOperate2Host);
	REGIS_HOST_HANDLER(PB_REPAIR_ITEM_CH, handleRepairItem2Host);
	REGIS_HOST_HANDLER(PB_BACKPACK_LOOT_CH, handleLootItem2Host);
	REGIS_HOST_HANDLER(PB_BACKPACK_STORE_CH, handleStoreItem2Host);
	REGIS_HOST_HANDLER(PB_CLOSE_CONTAINER_CH, handleCloseContainer2Host);
	REGIS_HOST_HANDLER(PB_CLOSEDIALOGUE_CH, handleCloseDialogue2Host);
	REGIS_HOST_HANDLER(PB_ANSWERTASK_CH, handleAnswerTask2Host);
	REGIS_HOST_HANDLER(PB_COMPLETE_TASK_CH, handleCompleteTask2Host);
	REGIS_HOST_HANDLER(PB_NEED_CONTAINER_PASSWORD_CH, handleNeedContainerPassword2Host);
	REGIS_HOST_HANDLER(PB_CHAT_CH, handleChat2Host);
	REGIS_HOST_HANDLER(PB_ACTORINVITE_CH, handleActorInvite2Host);
	REGIS_HOST_HANDLER(PB_YM_VOICE_CH, handleYMVoice2Host);
	REGIS_HOST_HANDLER(PB_PLAYER_MOUNTACTOR_CH, handlePlayerMount2Host);
	REGIS_HOST_HANDLER(PB_PLAYER_MOVEINPUT_CH, handlePlayerMoveInput2Host);
	REGIS_HOST_HANDLER(PB_PLAYER_SLEEP_CH, handlePlayerSleep2Host);
	REGIS_HOST_HANDLER(PB_NPCTRADE_CH, handleNpcTrade2Host);
	REGIS_HOST_HANDLER(PB_ROOM_JRUISDICTION_CH, handleApplyPermits2Host);
	REGIS_HOST_HANDLER(PB_GUN_INFO_CH, handleGunSpread2Host);
	REGIS_HOST_HANDLER(PB_SYNC_SETINFO_CH, handleSyncSetInfo2Host);
	REGIS_HOST_HANDLER(PB_SYNC_GRIDUSERDATA_CH, handleSyncGridUserData2Host);
	REGIS_HOST_HANDLER(PB_GUN_DORELOAD_CH, handleGunDoReload2Host);
	REGIS_HOST_HANDLER(PB_TRAIN_MOVE_CH, handleTrainMove2Host);
	REGIS_HOST_HANDLER(PB_YM_CHANGEROLE_CH, handleYMChangeRole2Host);
	REGIS_HOST_HANDLER(PB_SET_SPECTATORMODE_CH, handleSpectatorMode2Host);
	REGIS_HOST_HANDLER(PB_SET_SPECTATORTYPE_CH, handleSpectatorType2Host);
	REGIS_HOST_HANDLER(PB_SET_SPECTATOR_PLAYER_CH, handleSetSpetatorPlayer2Host);
	REGIS_HOST_HANDLER(PB_ACTOR_TELEPORT_CH, handleActorTeleport2Host);
	REGIS_HOST_HANDLER(PB_SET_PLAYER_MODEL_ANI_CH, handleSetPlayerModelAni2Host);
	REGIS_HOST_HANDLER(PB_SEND_VIEWMODE_SPECTATOR_CH, handleSetMyViewModeToSpectator2Host);
	REGIS_HOST_HANDLER(PB_SET_BOBBING_SPECTATOR_CH, handleSetBobblingToSpectator2Host);
	REGIS_HOST_HANDLER(PB_BALL_OPERATE_CH, handleBallOperate2Host);
	REGIS_HOST_HANDLER(PB_BASKETBALL_OPERATE_CH, handleBasketBallOperator2Host);
	REGIS_HOST_HANDLER(PB_ROCKET_TELEPORT_CH, handleRocketTeleport2Host);
	REGIS_HOST_HANDLER(PB_PLAYER_ACT_CH, handlePlayAct2Host);
	REGIS_HOST_HANDLER(PB_PLAYER_SKIN_ACT_CH, handlePlaySkinAct2Host); //2021-09-14 codeby:chenwei
	REGIS_HOST_HANDLER(PB_ACTOR_STOP_SKIN_ACT_CH, handleStopSkinAct2Host); //2021-10-08 codeby:chenwei
	REGIS_HOST_HANDLER(PB_ACTOT_SET_CUSTOM_CH, handlePlayerSkin2Host);
	REGIS_HOST_HANDLER(PB_CREATE_BLUEPRINT_HC, handleCreateBlueprint2Host);
	REGIS_HOST_HANDLER(PB_BLUEPRINT_PREBLOCK_CH, handleBluePrintPreBlock2Host);
	REGIS_HOST_HANDLER(PB_GRAVITY_OPERATE_CH, handleGravityOperate2Host);
	REGIS_HOST_HANDLER(PB_MAKE_CUSTOM_MODEL_CH, handleMakeCustomModel2Host);
	REGIS_HOST_HANDLER(PB_SELECT_MOB_SPAWN_CH, handleSelectMobSpawnBlock2Host);
	REGIS_HOST_HANDLER(PB_SAVE_TOMB_STONE_HC, handleSaveTombStone2Host);
	REGIS_HOST_HANDLER(PB_DEFORMATION_SKIN_CH, handleDeformationSkin2Host);
	REGIS_HOST_HANDLER(PB_RESETDEFORMATION_CH, handleResetDeformation2Host);
	REGIS_HOST_HANDLER(PB_RESTORE_DEFORMATION_CH, handleRestoreDefomation2Host);
	REGIS_HOST_HANDLER(PB_TRANSFER_RECORD_CH, handleTransfer2Host);
	REGIS_HOST_HANDLER(PB_TRANSFER_STATUS_CH, handleTransferStatus2Host);
	REGIS_HOST_HANDLER(PB_ACTOR_TRANSFER_CH, handleTransferTarget2Host);
	REGIS_HOST_HANDLER(PB_SYNC_LOVEAMBASSADOR_ICONID_CH, handleSyncLoveAmbassadorIcon2Host);
	REGIS_HOST_HANDLER(PB_NPCSHOP_GETSHOPINFO_CH, handleNpcShopGetInfo2Host);
	REGIS_HOST_HANDLER(PB_NPCSHOP_BUYSKU_CH, handleNpcShopBuySku2Host);
	REGIS_HOST_HANDLER(PB_PACKGIFT_NOTIFYITEMCHANGE_HC, handlePackGiftItemChg2Host);
	REGIS_HOST_HANDLER(PB_CLOSE_EDIT_ACTORMODEL_CH, handleCloseEditActorModel2Host);
	REGIS_HOST_HANDLER(PB_VEHICLE_PREBLOCK_CH, handleVehiclePreBlock2Host);
	REGIS_HOST_HANDLER(PB_VEHICLE_ITEMUSE_CH, handleVehicleItemUse2Host);
	REGIS_HOST_HANDLER(PB_VEHICLE_STARTBLOCK_CH, handleVehicleStartBlock2Host);
	REGIS_HOST_HANDLER(PB_VEHICLE_ATTRIB_CHANGE_CH, handleVehicleAttribChange2Host);
	REGIS_HOST_HANDLER(PB_WORKSHOP_ITEMINFO_CH, handleWorkshopItemInfo2Host);
	REGIS_HOST_HANDLER(PB_PLAYER_VEHICLE_MOVEINPUT_CH, handlePlayerVehicleMoveInput2Host);
	REGIS_HOST_HANDLER(PB_PLAYER_RESETVEHICLE_CH, handlePlayerResetVehicle2Host);
	REGIS_HOST_HANDLER(PB_PLAYER_MOTIONSTATECHANGE_CH, handlePlayerMotionStateChange2Host);
	REGIS_HOST_HANDLER(PB_PLAYER_CLICK_CH, handlePlayerClick2Host);
	REGIS_HOST_HANDLER(PB_PLAYER_SELECTSHORTCUT_CH, handlePlayerSelectShortcut2Host);
	REGIS_HOST_HANDLER(PB_TRIGGER_PLAYER_ATTRI_CH, handleTriggerPlayerAttri2Host);
	REGIS_HOST_HANDLER(PB_REQ_DOWNLOADRES_URL_CH, handleReqDownloadResUrl2Host);
	REGIS_HOST_HANDLER(PB_CLOSE_FULLYCUSTOMMODEL_UI_CH, handleCloseFullyCustomModelUI2Host);
	REGIS_HOST_HANDLER(PB_TRIGGER_SOUND_CH, handlePlayTriggerSound2Host);
	REGIS_HOST_HANDLER(PB_PLAYER_JUMP_CH, handlePlayerJumpOnce2Host);
	REGIS_HOST_HANDLER(PB_CLOUDSERVER_PERMIT_CH, handleCloudServerPlayerPermit2Host);
	REGIS_HOST_HANDLER(PB_CLOUDSERVER_AUTHORITY_CH, handleCloudServerAuthority2Host);
	REGIS_HOST_HANDLER(PB_SS_SYNC_TASK_CH, handleSSTask2Host);
	REGIS_HOST_HANDLER(PB_VEHICLE_ASSEMBLE_LINE_CH, handleVehicleAssembleLine2Host);
	REGIS_HOST_HANDLER(PB_VEHICLE_ASSEMBLE_LINE_OPERATE_CH, handleVehicleAssembleLineOperate2Host);
	REGIS_HOST_HANDLER(PB_ACTIONEDATA_UPDATE_CH, handleUpdateActionerData2Host);
	REGIS_HOST_HANDLER(PB_VEHICLE_WORKSHOP_LINE_CH, handleVehicleWorkshopLine2Host);
	REGIS_HOST_HANDLER(PB_CLOUDSERVER_CHANGE_TEAM_CH, handleCloudServerChangePlayerTeam2Host);
	// 给云服主机发送自动禁言
	REGIS_HOST_HANDLER(PB_CLOUDSERVER_ROOM_AUTOMUTE_CH, handleCloudServerAutoMute2Host);
	// 给主机发消息计算一下车间的连通然后同步过去
	REGIS_HOST_HANDLER(PB_VEHICLE_WORKSHOP_LINE_UPDATE_CH, handleWorkshopLineUpdate2Host);
	// 给主机同步客机地图编辑操作
	REGIS_HOST_HANDLER(PB_MAP_EDIT_HANDLE_CH, handleMapEditHandle2Host);
	// 给主机同步客机地图编辑撤销操作
	REGIS_HOST_HANDLER(PB_MAP_EDIT_REVOKE_CH, handleMapEditRevoke2Host);
	//云服主开启游戏
	REGIS_HOST_HANDLER(PB_CLOUD_ROOM_OWNER_START_GAME_CH, handleCloudRoomOnwerStartGame2Host);
	//云服踢人
	REGIS_HOST_HANDLER(PB_CLOUD_ROOM_KICK_OFF_CH, handleCloudRoomKickOff2Host);
	REGIS_HOST_HANDLER(PB_USE_PACKINGFCMITEM_CH, handleUsePackingFCMItem2Host);
	REGIS_HOST_HANDLER(PB_CREATE_PACKINGCM_CH, handleCreatePackingCM2Host);
	REGIS_HOST_HANDLER(PB_PLAYER_INPUTKEYS_CH, handlePlayerInputKeys2Host);
	REGIS_HOST_HANDLER(PB_PLAYER_INPUTCONTENT_CH, handlePlayerInputContent2Host);
	//感应方块客机修改了数据
	REGIS_HOST_HANDLER(PB_SENSOR_CONTAINER_DATA_CH, handleSensorContainerData2Host);
	REGIS_HOST_HANDLER(PB_PLAYER_CARRYACTOR_CH, handleCarryActor2Host);
	REGIS_HOST_HANDLER(PB_MOVE_MOBBACKPACKITEM_CH, handleMoveMobBackpack2Host);
	REGIS_HOST_HANDLER(PB_INTERACT_MOBBACKPACKITEM_CH, handleMobInteractItem2Host);
	REGIS_HOST_HANDLER(PB_PLAYER_PUSH_ARCH_CH, handleRolePushArch2Host);
	//召唤宠物
	REGIS_HOST_HANDLER(PB_HOME_SUMMONPET_CH, handleSummonPet2Host);
	//语音举报
	REGIS_HOST_HANDLER(PB_VOICE_INFORM_CH, handleVoiceInform2Host);
	REGIS_HOST_HANDLER(PB_REQUEST_MODEL_CH, handleRequestAvtarModel2Host);
	//设置熔炉温度
	REGIS_HOST_HANDLER(PB_FURNACE_TEMPERATURE_CH, handleFurnaceTemperature2Host);
	REGIS_HOST_HANDLER(PB_POT_CONTAINER_SET_MAKE_CH, handlePotStartMake2Host);
	REGIS_HOST_HANDLER(PB_TAKE_CONTAINER_ITEM_CH, handleTakePotItem2Host);
	//刷新复活点
	REGIS_HOST_HANDLER(PB_PLAYER_REVIVEPOINT_CH, handlePlayerRevivePoint2Host);
	REGIS_HOST_HANDLER(PB_STARSTATION_CHANGENAMESTATUS_CH, handleStarStationChangeNameStatus2Host);
	REGIS_HOST_HANDLER(PB_LEAVE_STARSTATION_CABIN_CH, handleLeaveStarStationCabin2Host);
	REGIS_HOST_HANDLER(PB_UPDATE_STARSTATION_CABIN_STATUS_CH, handleUpdateStarStationCabinStatus2Host);
	REGIS_HOST_HANDLER(PB_UPDATE_STARSTATION_CABIN_STATUSEND_CH, handleUpdateStarStationStatusEnd2Host);
	REGIS_HOST_HANDLER(PB_ADD_STARSTATION_TRANSFER_DESC_CH, handleAddStarStationTransferDesc2Host);
	REGIS_HOST_HANDLER(PB_ADD_UNFINISHED_TRANSFER_RECORD_CH, handleAddUnfinishedTransferRecord2Host);
	REGIS_HOST_HANDLER(PB_REMOVE_UNFINISHED_TRANSFER_RECORD_CH, handleRemoveUnfinishedTransferRecord2Host);
	REGIS_HOST_HANDLER(PB_PLAYER_TRANSFER_BY_STRSTATION_CH, handleStarStationTransferTarget2Host);
	REGIS_HOST_HANDLER(PB_REQUIRE_STARSTATION_TRANSFER_CH, handleStarStationRequest2Host);
	//主机通知客机增加指定数量的道具至背包
	REGIS_HOST_HANDLER(PB_GAINITEMSTOBACKPACK_CH, handleGainItemsToBackPack2Host);
	REGIS_HOST_HANDLER(PB_COUSTOMUI_EVENT_CH, handleCoustomUi2Host);
	REGIS_HOST_HANDLER(PB_ACHIEVEMENT_UPDATE_CH, handleUpdateAchievement2Host);
	// 客机通知主机添加/扣除星星币
	REGIS_HOST_HANDLER(PB_ADDEXP_CH, handleNewAdNpcAddExp2Host);
	REGIS_HOST_HANDLER(PB_USEHEARTH_CH, handldUseHearth2Host);
	//916冒险 2021/08/18 codeby:wudeshen
	REGIS_HOST_HANDLER(PB_ANSWER_LANTERNBIRD_CH, handleAnswerLanternBird2Host);
	REGIS_HOST_HANDLER(PB_PLAYER_CLOSEUI_CH, handlePlayerCloseUI2Host);
	//20210823：客机通知主机交换指定数量的道具至背包 codeby: wangyu
	REGIS_HOST_HANDLER(PB_EXCHANGEITEMSTOBACKPACK_CH, handleExchangeItemsToBackPack2Host);
	REGIS_HOST_HANDLER(PB_CHANGE_QQMUSIC_PLAYER_CH, handleChangeQQMusicPlayer2Host);
	REGIS_HOST_HANDLER(PB_MINICLUB_PLAYER_CH, handleMiniClubPlayer2Host);
	REGIS_HOST_HANDLER(PB_CUSTOM_MSG, handleCustomMsg2Host);
	REGIS_HOST_HANDLER(PB_HOME_PRAY_TIME_CH, handlePrayTimeHost);
	REGIS_HOST_HANDLER(PB_HOMELAND_RANCH_ANIMAL_UPDATE_CH, handleHomelandRanchAnimalState2Host);
	REGIS_HOST_HANDLER(PB_HOMELAND_RANCH_FOODER_CH, handleHomelandRanchFooderInfo2Host);
	REGIS_HOST_HANDLER(PB_HOMELAND_COOK_MENUBUY_CH, handleHomeLandMenuBuySuccess2Host);
	REGIS_HOST_HANDLER(PB_HOMELAND_FARM_SHOP_CH, handleHomeLandShopCell2Host);
	REGIS_HOST_HANDLER(PB_HOMELAND_COOK_SPFURNITUREBUY_CH, handleHomeLandSpecialFurnitureBuySuccess2Host);
	
	//20211020 注册喷漆同步事件 codeby:柯冠强 
	REGIS_HOST_HANDLER(PB_SPRAY_PAINT_INFO_CH, handleSprayPaintInfo2Host);

	REGIS_HOST_HANDLER(PB_BUY_AD_SHOP_GOOD_CH, handleAdShopBuy2Host);
	// 成就奖励获取
	REGIS_HOST_HANDLER(PB_ACHIEVEMENT_AWARD_CH, handleAchievementAward2Host);
	REGIS_HOST_HANDLER(PB_SYNC_CLIENT_ACTIONLOG_CH, handleClientActionLog2Host);
	REGIS_HOST_HANDLER(PB_UPLOAD_CHECK_INFO_CH, handleCheckInfo2Host);
	REGIS_HOST_HANDLER(PB_GET_ADSHOP_EXTRA_AWARD_CH, handleGetAdShopExtraAward2Host);
	REGIS_HOST_HANDLER(PB_EXTRACT_STORE_ITEM_CH, handleExtraStoreItem2Host);

	//20211101 手持物品 codeby:luoshuai
	REGIS_HOST_HANDLER(PB_Equip_Weapon_CH, handlePlayerEquipWeapon2Host);
	//436138 wangshuai
	REGIS_HOST_HANDLER(PB_PLAYEFFECT_CH, handlePlayEffect2Host);
	//20210915 音乐方块 codeby:huangxin
	REGIS_HOST_HANDLER(PB_CHANGE_QQMUSIC_CLUB_CH, handleChangeQQMusicClub2Host);
	//主机通知客机增加指定数量的道具至背包,带参数userdatastr
	REGIS_HOST_HANDLER(PB_GainItemsUserDatastrToBackPack_CH, handleGainItemsUserDataStrToBackPack2Host);
	REGIS_HOST_HANDLER(PB_UseMusicYuPu_CH, handleUseMusicYuPu2Host);
	REGIS_HOST_HANDLER(PB_DanceByPlaying_CH, handleDanceByPlaying2Host);
	REGIS_HOST_HANDLER(PB_StopDanceByPlaying_CH, handleStopDanceByPlaying2Host);
	REGIS_HOST_HANDLER(PB_ACTOR_STOP_ANIM_CH, handleActorStopAnim2Host);
	REGIS_HOST_HANDLER(PB_StartAct_CH, handleStartPlayingPiano2Host);
	REGIS_HOST_HANDLER(PB_StopAct_CH, handleStopPlayingPiano2Host);
	REGIS_HOST_HANDLER(PB_TOP_BRAND_CH, handleShowTopBrand2Host);
	REGIS_HOST_HANDLER(PB_CHEAT_CHECK_CH, handleCheatCheck2Host);
	REGIS_HOST_HANDLER(PB_PVP_ACTIVITY_CONFIG_CH, handlePvpActivityConfig2Host);
	REGIS_HOST_HANDLER(PB_UPLOAD_CLIENT_INFO_CH, handleSetClientPlayerInfo2Host);

	//20221118 钓鱼 codeby shitengkai
	REGIS_HOST_HANDLER(PB_STARTFISHING_CH, handleStartFishing2Host);
	REGIS_HOST_HANDLER(PB_ENDFISHING_CH, handleEndFishing2Host);
	REGIS_HOST_HANDLER(PB_QUITFISHING_CH, handleQuitFishing2Host);
	REGIS_HOST_HANDLER(PB_END_PLAY_FISH_CH, handleEndPlayFish2Host);

	//小地图
	REGIS_HOST_HANDLER(PB_CHANGEEXPOSEPOS_CH, handleExposePos2Host);
	//20230309 黑板文字同步 codeby:huangxin
	REGIS_HOST_HANDLER(PB_BlockData_CH, handleChangeBlockData2Host);

	REGIS_HOST_HANDLER(PB_PUSHSNOWBALL_OPERATE_CH, handlePushSnowBallOperate2Host);
	REGIS_CLIENT_HANDLER(PB_PUSHSNOWBALL_SIZECHANGE_HC, handlePushSnowBallSizeChange2Client);
	//播放预警特效
	REGIS_CLIENT_HANDLER(PB_PLAY_EFFECT_SHADER_HC, handlePlayEffectShader2Client);

	REGIS_HOST_HANDLER(PB_PLAYWEAPONEFFECT_CH, handlePlayWeaponEffectHost);
	REGIS_HOST_HANDLER(PB_ACTOR_PLAY_ANIM_CH, handlePlayAnimation2Host);
	REGIS_HOST_HANDLER(PB_ACTOR_ATTACK_CH, handleActorAttack2Host);
	REGIS_HOST_HANDLER(PB_ACTOR_DEFANCESTATE_CH, handleActorDefanceState2Host);

	REGIS_HOST_HANDLER(PB_SYNC_MOVE_CH, handleSyncMove2Host);
	REGIS_CLIENT_HANDLER(PB_RESET_ROLE_FLAGS, handleResetRoleFlags2Client);
	REGIS_HOST_HANDLER(PB_NEW_REPAIR_ITEM_CH, handleNewRepairItem2Host);
	REGIS_HOST_HANDLER(PB_ACTOR_PLAY_SOUND_CH, handleActorPlaySound2Host);

	REGIS_HOST_HANDLER(PB_BIND_PLAYER_TO_PHYSICS_PLAT_CH, handleBindPlayerToPhysicsPlat2Host);
	REGIS_HOST_HANDLER(PB_UNBIND_PLAYER_TO_PHYSICS_PLAT_CH, handleUnBindPlayerToPhysicsPlat2Host);
	REGIS_HOST_HANDLER(PB_SYNC_PLAYER_POS_CH, handleRespSyncMove2Host);

	//玩家救援
	REGIS_HOST_HANDLER(PB_PLAYER_REVIVE_REQUEST_CH, handlePlayerReviveRequest2host);
	//射击（新枪械）
	REGIS_HOST_HANDLER(PB_ACTORSHOOT_CH, handleActorShoot2Host);
	REGIS_HOST_HANDLER(PB_ACTOR_FIREWORK_CH, handleActorFirework2Host);
}

void MpGameSurviveNetHandler::registerClientNetHandler()
{
	//GetGameNetManagerPtr()->registerClientHandler(PB_HEARTBEAT_HC, std::bind(&MpGameSurviveNetHandler::handleHeartBeat2Client, this, std::placeholders::_1));

	REGIS_CLIENT_HANDLER(PB_HEARTBEAT_HC, handleHeartBeat2Client);
	REGIS_CLIENT_HANDLER(PB_SYNC_CHUNK_DATA_HC, handleSyncChunkData2Client);
	REGIS_CLIENT_HANDLER(PB_BLOCK_DATA_UPDATE_HC, handleBlockUpdate2Client);
	REGIS_CLIENT_HANDLER(PB_ROLE_ENTER_WORLD_HC, handleRoleEnterWorld2Client);
	REGIS_CLIENT_HANDLER(PB_ACTOR_ENTER_AOI_HC, handleActorEnterAoi2Client);
	REGIS_CLIENT_HANDLER(PB_ACTOR_LEAVE_AOI_HC, handleActorLeaveAoi2Client);
	REGIS_CLIENT_HANDLER(PB_GENERAL_ENTER_AOI_HC, handleGeneralEnterAoi2Client);
	REGIS_CLIENT_HANDLER(PB_ACTOR_MOVE_HC, handleActorMove2Client);
	REGIS_CLIENT_HANDLER(PB_ACTOR_MOVEV2_HC, handleActorMoveV22Client);
	REGIS_CLIENT_HANDLER(PB_ACTOR_MOVEV3_HC, handleActorMoveV32Client);
	REGIS_CLIENT_HANDLER(PB_ACTOR_MODELCHG_HC, handleActorModelChgClient);
	REGIS_CLIENT_HANDLER(PB_FULLROT_ACTOR_MOVE_HC, handleFullrotActorMove2Client);
	REGIS_CLIENT_HANDLER(PB_TRAIN_MOVE_HC, handleTrainMove2Client);
	REGIS_CLIENT_HANDLER(PB_ACTOR_MOTION_HC, handleActorMotion2Client);
	REGIS_CLIENT_HANDLER(PB_ACTOR_MOTIONV2_HC, handleActorMotionV22Client);
	REGIS_CLIENT_HANDLER(PB_MECHA_MOTION_HC, handleMechaMotion2Client);
	REGIS_CLIENT_HANDLER(PB_WORLD_SYNC_SAVE_HC, handleSyncSaveWorld);
	REGIS_CLIENT_HANDLER(PB_ACTOR_REVIVE_HC, handleActorRevive2Client);
	REGIS_CLIENT_HANDLER(PB_ACTOR_TELEPORT_HC, handleActorTeleport2Client);
	REGIS_CLIENT_HANDLER(PB_BACKPACK_GRID_UPDATE_HC, handleBackPackGridUpdate2Client);
	REGIS_CLIENT_HANDLER(PB_BACKPACK_EQUIP_WEAPON_HC, handleBackPackGridEquipWeapon2Client);
	REGIS_CLIENT_HANDLER(PB_ACTOR_EQUIP_ITEM_HC, handleActorEquipItem2Client);
	REGIS_CLIENT_HANDLER(PB_OPEN_CONTAINER_HC, handleOpenContainer2Client);
	REGIS_CLIENT_HANDLER(PB_NEED_CONTAINER_PASSWORD_HC, handleNeedContainerPassword2Client);
	REGIS_CLIENT_HANDLER(PB_CLOSE_CONTAINER_HC, handleCloseContainer2Client);
	REGIS_CLIENT_HANDLER(PB_CLOSEDIALOGUE_HC, handleCloseDialogue2Client);
	REGIS_CLIENT_HANDLER(PB_UPDATETASK_HC, handleUpdateTask2Client);
	REGIS_CLIENT_HANDLER(PB_UPDATE_CONTAINER_HC, handleUpdateContainer2Client);
	REGIS_CLIENT_HANDLER(PB_ENCHANT_ITEM_SUCCESS_HC, handleEnchantSuccess2Client);
	REGIS_CLIENT_HANDLER(PB_RUNE_OPERATE_SUCCESS_HC, handleRuneOperateSuccess2Client);
	REGIS_CLIENT_HANDLER(PB_REPAIR_ITEM_SUCCESS_HC, handleRepairSuccess2Client);
	REGIS_CLIENT_HANDLER(PB_OPEN_HOMENPC_HC, handleOpenHomeNpc2Client);
	REGIS_CLIENT_HANDLER(PB_UPDATE_POT_CONTAINER_HC, handleUpdatePotContainer2Client);
	REGIS_CLIENT_HANDLER(PB_ACTOR_ANIM_HC, handleActorAnim2Client);
	REGIS_CLIENT_HANDLER(PB_ACTOR_STOP_ANIM_HC, handleActorStopAnim2Client);
	REGIS_CLIENT_HANDLER(PB_ACTOR_ATTR_CHANGE_HC, handleActorAttrChange2Client);
	REGIS_CLIENT_HANDLER(PB_ACTOR_BUFF_CHANGE_HC, handleActorBuffChange2Client);
	REGIS_CLIENT_HANDLER(PB_PLAYER_ATTR_CHANGE_HC, handlePlayerAttrChange2Client);
	REGIS_CLIENT_HANDLER(PB_GAME_LEADER_SWITCH_HC, handleGameLeaderSwitch2Client);
	REGIS_CLIENT_HANDLER(PB_CHAT_HC, handleChat2Client);
	REGIS_CLIENT_HANDLER(PB_ACTORINVITE_HC, handleActorInvite2Client);
	REGIS_CLIENT_HANDLER(PB_WGLOBAL_UPDATE_HC, handleWGlobalUpdate2Client);
	REGIS_CLIENT_HANDLER(PB_PLAYERS_UPDATEINFO_HC, handlePlayerInfoUpdate2Client);
	REGIS_CLIENT_HANDLER(PB_PLAYER_LEAVE_HC, handlePlayerLeave2Client);
	REGIS_CLIENT_HANDLER(PB_TEAM_SCORE_HC, handleSyncTeamScore2Client);
	REGIS_CLIENT_HANDLER(PB_SET_TEAM_HC, handleSetTeamID2Client);
	REGIS_CLIENT_HANDLER(PB_SET_PLAYER_GAME_INFO_HC, handleSetPlayerGameInfo2Client);
	REGIS_CLIENT_HANDLER(PB_CGAMESTAGE_HC, handleCGameStage2Client);
	REGIS_CLIENT_HANDLER(PB_PLAYERPERMIT_HC, handlePlayerPermits2Client);
	REGIS_CLIENT_HANDLER(PB_GAME_TIPS_HC, handleGameTips2Client);
	REGIS_CLIENT_HANDLER(PB_PLAYEFFECT_HC, handlePlayEffect2Client);
	REGIS_CLIENT_HANDLER(PB_PLAYEFFECT_HC_V2, handlePlayEffect2Client_V2);
	REGIS_CLIENT_HANDLER(PB_EFFECTSCALE_HC, handleEffectScale2Client);
	REGIS_CLIENT_HANDLER(PB_PLAYWEAPONEFFECT_HC, handlePlayWeaponEffectClient);
	REGIS_CLIENT_HANDLER(PB_SCRIPTVAR_HC, handleScriptVars2Client);
	REGIS_CLIENT_HANDLER(PB_ACCOUNT_HORSE_HC, handleAccountHorse2Client);
	REGIS_CLIENT_HANDLER(PB_YM_VOICE_HC, handleYMVoice2Client);
	REGIS_CLIENT_HANDLER(PB_SKILLCD_HC, handleSkillCD2Client);
	REGIS_CLIENT_HANDLER(PB_HORSE_SKILLCD_HC, handleHorseSkillCD2Client);
	REGIS_CLIENT_HANDLER(PB_VACANT_BOSS_STATE_HC, handleVaCantBossStateClient);
	REGIS_CLIENT_HANDLER(PB_BLOCK_INTERACT_HC, handleBlockInteract2Client);
	REGIS_CLIENT_HANDLER(PB_BLOCK_PUNCH_HC, handleBlockPunch2Client);
	REGIS_CLIENT_HANDLER(PB_BLOCK_EXPLOIT_HC, handleBlockExploit2Client);
	REGIS_CLIENT_HANDLER(PB_ITEM_USE_HC, handleItemUse2Client);
	REGIS_CLIENT_HANDLER(PB_SET_HOOK_HC, handleSetHook2Client);
	REGIS_CLIENT_HANDLER(PB_ITEM_SKILL_USE_HC, handleItemSkillUse2Client);
	REGIS_CLIENT_HANDLER(PB_ACTOR_INTERACT_HC, handleActorInteract2Client);
	REGIS_CLIENT_HANDLER(PB_RCLICKUP_INTERACT_HC, handleRClickUpInteract2Client);
	REGIS_CLIENT_HANDLER(PB_RCLICKUP_INTERACT_HC, handlePlayerWakeUp2Client);
	REGIS_CLIENT_HANDLER(PB_PLAYER_MOUNTACTOR_HC, handlePlayerMount2Client);
	REGIS_CLIENT_HANDLER(PB_ACTOR_MOUNTACTOR_HC, handleActorMount2Client);
	REGIS_CLIENT_HANDLER(PB_ACTOR_REVERSE_HC, handleActorReverseClient);
	REGIS_CLIENT_HANDLER(PB_ACTOR_BIND_HC, handleActorBindClient);
	REGIS_CLIENT_HANDLER(PB_PLAYER_SLEEP_HC, handlePlayerSleep2Client);
	REGIS_CLIENT_HANDLER(PB_MOB_BODY_CHANGE_HC, handleMobBodyChange2Client);
	REGIS_CLIENT_HANDLER(PB_OPENWINDOW_HC, handleOpenWindow2Client);
	REGIS_CLIENT_HANDLER(PB_SPECIALITEM_USE_HC, handleSpecialItemUse2Client);
	REGIS_CLIENT_HANDLER(PB_LEAVE_ROOM_INFO_HC, handleLeaveRoomInfo2Client);
	REGIS_CLIENT_HANDLER(PB_ROOM_JRUISDICTION_HC, handleReplyApplyPermits2Client);
	REGIS_CLIENT_HANDLER(PB_GUN_DORELOAD_HC, handleGunDoReload2Client);
	REGIS_CLIENT_HANDLER(PB_SYNC_GRIDUSERDATA_HC, handleSyncGridUserData2Client);
	REGIS_CLIENT_HANDLER(PB_SYNC_TRIGGERBLOCK_HC, handleSyncTriggerBlock2Client);
	REGIS_CLIENT_HANDLER(PB_INVITEJOINROOM_HC, handleInviteJoinRoomClient);
	REGIS_CLIENT_HANDLER(PB_YM_CHANGEROLE_HC, handleYMChangeRoleClient);
	REGIS_CLIENT_HANDLER(PB_SET_SPECTATORMODE_HC, handleSpectatorMode2Client);
	REGIS_CLIENT_HANDLER(PB_SET_SPECTATORTYPE_HC, handleSpectatorType2Client);
	REGIS_CLIENT_HANDLER(PB_SET_SPECTATOR_PLAYER_HC, handleSetSpetatorPlayer2Client);
	REGIS_CLIENT_HANDLER(PB_SET_PLAYER_MODEL_ANI_HC, handleSetPlayerModelAni2Client);
	REGIS_CLIENT_HANDLER(PB_SEND_VIEWMODE_SPECTATOR_HC, handleSetMyViewModeToSpectator2Client);
	REGIS_CLIENT_HANDLER(PB_SET_BOBBING_SPECTATOR_HC, handleSetBobblingToSpectator2Client);
	REGIS_CLIENT_HANDLER(PB_BALL_OPERATE_HC, handleBallOperate2Client);
	REGIS_CLIENT_HANDLER(PB_BASKETBALL_OPERATE_HC, handleBasketBallOperator2Client);
	REGIS_CLIENT_HANDLER(PB_RESET_ROUND_HC, handleResetRound2Client);
	REGIS_CLIENT_HANDLER(PB_ROCKET_ATTRIB_CHANGE_HC, handleRocketAttrChange2Client);
	REGIS_CLIENT_HANDLER(PB_WORLD_TIMES_HC, handleWorldTimes2Client);
	REGIS_CLIENT_HANDLER(PB_STATISTIC_HC, handleStatistic2Client);
	REGIS_CLIENT_HANDLER(PB_TOTEMPOINT_HC, handleSyncTotemPoint2Client);
	REGIS_CLIENT_HANDLER(PB_HORSEFLYSTATE_HC, handleHorsreFlyState2Client);
	REGIS_CLIENT_HANDLER(PB_OPENDIALOGUE_HC, handleOpenDialogue2Client);
	REGIS_CLIENT_HANDLER(PB_SYNCTASK_ENTERWORLD_HC, handleSyncTaskEnterWorld2Client);
	REGIS_CLIENT_HANDLER(PB_COMPLETE_TASK_HC, handleCompleteTask2Client);
	REGIS_CLIENT_HANDLER(PB_ATTRACT_ATTRIB_CHANGE_HC, handleAttractAttrChange2Client);
	REGIS_CLIENT_HANDLER(PB_ACTOR_BODY_TEXTURE_HC, handleActorBodyTexture2Client);
	REGIS_CLIENT_HANDLER(PB_PLAYER_ADDAVARTAR_HC, handlePlayerAddAvartar2Client);
	REGIS_CLIENT_HANDLER(PB_PLAYER_CHANGEMODEL_HC, handlePlayerChangeModel2Client);
	REGIS_CLIENT_HANDLER(PB_PLAYERTRANSFORMSKIN_HC, handlePlayerTransformSkinModel2Client);
	REGIS_CLIENT_HANDLER(PB_PLAYER_AVARTARCOLOR_HC, handlePlayerAvartarColor2Client);
	REGIS_CLIENT_HANDLER(PB_PLAYER_ACT_HC, handlePlayAct2Client);
	REGIS_CLIENT_HANDLER(PB_PLAYER_SKIN_ACT_HC, handlePlaySkinAct2Client);  //2021-09-14 codeby:chenwei
	REGIS_CLIENT_HANDLER(PB_ACTOR_STOP_SKIN_ACT_HC, handleStopSkinAct2Client); //2021-10-08 codeby:chenwei
	REGIS_CLIENT_HANDLER(PB_ACTOT_SET_CUSTOM_HC, handlePlayerSkin2Client);
	REGIS_CLIENT_HANDLER(PB_MEASURE_DISTANCE_HC, handleMeasureDistance2Client);
	REGIS_CLIENT_HANDLER(PB_BLUEPRINT_PREBLOCK_HC, handleBluePrintPreBlock2Client);
	REGIS_CLIENT_HANDLER(PB_GRAVITY_OPERATE_HC, handleGravityOperate2Client);
	REGIS_CLIENT_HANDLER(PB_PLAYER_BODY_COLOR_HC, handlePlayerBodyColor2Client);
	REGIS_CLIENT_HANDLER(PB_CUSTOM_MODEL_HC, handleCustomModel2Client);
	REGIS_CLIENT_HANDLER(PB_CUSTOM_MODEL_PRE_HC, handleCustomModelPre2Client);
	REGIS_CLIENT_HANDLER(PB_CUSTOM_ITEMIDS_HC, handleCustomItemIDs2Client);
	REGIS_CLIENT_HANDLER(PB_PLAYER_SPAWN_POINT_HC, handlePlayerSpawnPoint2Client);
	REGIS_CLIENT_HANDLER(PB_CUSTOM_MODELCLASS_HC, handleCustomModelClass2Client);
	REGIS_CLIENT_HANDLER(PB_PLAYER_CAMERAROTATE_HC, handlePlayerCameraRotate2Client);
	REGIS_CLIENT_HANDLER(PB_PLAYER_CHANGEVIEWMODE_HC, handlePlayerChangeViewMode2Client);
	REGIS_CLIENT_HANDLER(PB_PLAYER_CANMOVE_HC, handlePlayerCanMove2Client);
	REGIS_CLIENT_HANDLER(PB_PLAYER_CANCONTROL_HC, handlePlayerCanControl2Client);
	REGIS_CLIENT_HANDLER(PB_PLAYER_SETATTR_HC, handlePlayerSetAttr2Client);
	REGIS_CLIENT_HANDLER(PB_PLAYER_FREEZING_HC, handlePlayerFreezing2Client);
	REGIS_CLIENT_HANDLER(PB_TRIGGER_MUSIC_HC, handleTriggerMusic2Client);
	REGIS_CLIENT_HANDLER(PB_TRIGGER_OPENSTORE_HC, handleTriggerOpenStore2Client);
	REGIS_CLIENT_HANDLER(PB_PLAYER_ATTR_SCALE_HC, handlePlayerAttrScale2Client);
	REGIS_CLIENT_HANDLER(PB_PLAYER_NAVIGATE_HC, handlePlayerNavigate2Client);
	REGIS_CLIENT_HANDLER(PB_PLAYER_FACE_YAW_HC, handlePlayerFaceYaw2Client);
	REGIS_CLIENT_HANDLER(PB_PLAYER_JUMP_HC, handlePlayerJumpOnce2Client);
	REGIS_CLIENT_HANDLER(PB_CLOUD_ROOM_STATUSTIME_HC, handlePlayerCloudRoomStatusTime2Client);
	REGIS_CLIENT_HANDLER(PB_VILLAGER_BODY_CHANGE_HC, handleVillagerBodyChange2Client);
	REGIS_CLIENT_HANDLER(PB_VILLAGER_CLOTH_HC, handleVillagerCloth2Client);




	REGIS_CLIENT_HANDLER(PB_TRANSFER_RECORD_HC, handleTransfer2Client);
	REGIS_CLIENT_HANDLER(PB_TRANSFER_STATUS_HC, handleTransferStatus2Client);
	REGIS_CLIENT_HANDLER(PB_TRANSFER_ADD_DEL_HC, handleTransferAddDel2Client);
	REGIS_CLIENT_HANDLER(PB_TRANSFER_DATA_HC, handleTransferData2Client);
	REGIS_CLIENT_HANDLER(PB_ACTOR_TRANSFER_HC, handleTransferUI2Client);
	REGIS_CLIENT_HANDLER(PB_SYNC_LOVEAMBASSADOR_ICONID_HC, handleSyncLoveAmbassadorIcon2Client);
	REGIS_CLIENT_HANDLER(PB_NPCSHOP_RESPGETSHOPINFO_HC, handleNpcShopRespGetInfo2Client);
	REGIS_CLIENT_HANDLER(PB_NPCSHOP_NOTIFYBUY_HC, handleNpcShopNotifyBuySku2Client);


	REGIS_CLIENT_HANDLER(PB_VEHICLE_MOVE_HC, handleVehicleMove2Client);
	REGIS_CLIENT_HANDLER(PB_OPEN_EDIT_ACTORMODEL_HC, handleOpenEditActorModel2Client);

	REGIS_CLIENT_HANDLER(PB_CLOSE_EDIT_ACTORMODEL_HC, handleCloseEditActorModel2Client);
	REGIS_CLIENT_HANDLER(PB_CUSTOMACTOR_MODELDATA_HC, handleCustomActorModelData2Client);
	REGIS_CLIENT_HANDLER(PB_VEHICLE_PREBLOCK_HC, handleVehiclePreBlock2Client);


	REGIS_CLIENT_HANDLER(PB_VEHICLE_ALL_ITEMID_HC, handleVehicleAllItemid2Client);
	REGIS_CLIENT_HANDLER(PB_VEHICLE_ONE_ITEMID_HC, handleVehicleOneItemid2Client);
	REGIS_CLIENT_HANDLER(PB_VEHICLE_ATTRIB_CHANGE_HC, handleVehicleAttribChange2Client);

	REGIS_CLIENT_HANDLER(PB_WORKSHOP_ITEMINFO_HC, handleWorkshopItemInfo2Client);

	REGIS_CLIENT_HANDLER(PB_WORKSHOP_BUILD_HC, handleWorkshopBuild2Client);


	REGIS_CLIENT_HANDLER(PB_VEHICLEASSEMBLEBLOCK_UPDATE_HC, handleVehicleAssembleBlockUpdate2Client);
	REGIS_CLIENT_HANDLER(PB_VEHICLEASSEMBLEBLOCK_ALL_HC, handleVehicleAssembleBlockAll2Client);

	REGIS_CLIENT_HANDLER(PB_TRIGGER_TIMER_HC, handleTriggerTimer2Client);
	REGIS_CLIENT_HANDLER(PB_GAMERULE_HC, handleGameRule2Client);

	REGIS_CLIENT_HANDLER(PB_OPENEDIT_FULLYCUSTOMMODEL_HC, handleOpenEditFullyCustomModel2Client);
	REGIS_CLIENT_HANDLER(PB_CLOSE_FULLYCUSTOMMODEL_UI_HC, handleCloseEditFullyCustomModel2Client);


	REGIS_CLIENT_HANDLER(PB_RESP_DOWNLOADRES_URL_HC, handleRespDownLoadRes2Client);
	REGIS_CLIENT_HANDLER(PB_PRE_OPEN_EDIT_FCM_UI, handlePreOpenEditFCMUI2Client);


	REGIS_CLIENT_HANDLER(PB_CLOUDSERVER_PERMIT_HC, handleCloudServerPlayerPermit2Client);
	// 下发超管、管理员信息
	REGIS_CLIENT_HANDLER(PB_CLOUDSERVER_AUTHORITY_HC, handleCloudServerAuthority2Client);

	// 触发器同步任务
	REGIS_CLIENT_HANDLER(PB_SS_SYNC_TASK_HC, handleSSTask2Client);


	REGIS_CLIENT_HANDLER(PB_VEHICLE_ASSEMBLE_LINE_HC, handleVehicleAssembleLine2Client);


	REGIS_CLIENT_HANDLER(PB_VEHICLE_BIND_ACTOR_HC, handleVehicleBindActor2Client);

	REGIS_CLIENT_HANDLER(PB_VEHICLE_ASSEMBLE_LINE_OPERATE_HC, handleVehicleAssembleLineOperate2Client);
	REGIS_CLIENT_HANDLER(PB_CLOUDSERVER_CHANGE_STATE_HC, handleCloudServerChangeState2Client);
	REGIS_CLIENT_HANDLER(PB_USE_PACKINGFCMITEM_HC, handleUsePackingFCMItem2Client);
	REGIS_CLIENT_HANDLER(PB_CREATE_PACKINGCM_HC, handleCreatePackingCM2Client);
	REGIS_CLIENT_HANDLER(PB_PACKING_FCMDATA_HC, handlePackingFCMData2Client);





	REGIS_CLIENT_HANDLER(PB_SENSOR_CONTAINER_DATA_HC, handleSensorContainerData2Client);

	//更新客机的刚体
	REGIS_CLIENT_HANDLER(PB_DOOR_DATA_HC, handleDoorData2Client);


	REGIS_CLIENT_HANDLER(PB_PLAYER_CARRYACTOR_HC, handleCarryActor2Client);

	REGIS_CLIENT_HANDLER(PB_PLAYER_TAME_ACTOR_HC, handleTameActor2Client);
	REGIS_CLIENT_HANDLER(PB_ACTOR_HEAD_DISPLAY_ICON_HC, handleActorHeadDisplayIcon2Client);
	REGIS_CLIENT_HANDLER(PB_ACTOR_PLAY_ANIM_BY_ID_HC, handleActorPlayAnimById2Client);

	REGIS_CLIENT_HANDLER(PB_PLAYER_LEVELMODE_HC, handlePlayerLevelMode2Client);

	REGIS_CLIENT_HANDLER(PB_ACTION_ATTR_STATE_HC, handleActionAttrState2Client);

	REGIS_CLIENT_HANDLER(PB_CRAFTING_QUEUE_QUEUE_UPDATE_HC, handleCraftingQueueUpdate2Client);

	REGIS_CLIENT_HANDLER(PB_PLAYER_DOWNED_STATE_CHANGE_HC, handlePlayerDownedStateChange2Client);

	//图腾提示
	REGIS_CLIENT_HANDLER(PB_VILLAGE_TOTEM_TIP_HC, handleVillageTotemTip2Client);
	REGIS_CLIENT_HANDLER(PB_VILLAGE_TOTEM_ACTIVE_HC, handleVillageTotemActive2Client);

	//导入模型数据
	REGIS_CLIENT_HANDLER(PB_IMPORT_MODEL_HC, handleImportModel2Client);

	//玩家存档数据
	REGIS_CLIENT_HANDLER(PB_PLAYER_SAVE_ARCH_HC, handlePlayerArch2Client);

	REGIS_CLIENT_HANDLER(PB_LIGHTNING_HC, handleLightning2Client);
	REGIS_CLIENT_HANDLER(PB_INTERACT_MOBPACK_HC, handleInteractMobPack2Client);

	REGIS_CLIENT_HANDLER(PB_UPDATE_MOB_BACKPACK_HC, handleUpdateMobBackpack2Client);




	//春节活动
	REGIS_CLIENT_HANDLER(PB_SFACTIVITY_HC, handleSFActivityClient);


	REGIS_CLIENT_HANDLER(PB_OPEN_DEVGOODSBUY_DIALOGHC, handleOpenDevGoodsBuyDialogClient);

	REGIS_CLIENT_HANDLER(PB_TRIGGER_GRAPHICS_HC, handleChangeGraphics2Client);
	REGIS_CLIENT_HANDLER(PB_GODTEMPLE_CREATE_HC, handleGodTempleCreate2Client);


	REGIS_CLIENT_HANDLER(PB_SHAPE_ADDITION_ANIM_HC, handleShapeAdditionAnim2Client);

	// 基础设置自定义模型
	REGIS_CLIENT_HANDLER(PB_PLAYER_CUSTOM_BASEMODEL_HC, handleCustomBaseModel2Client);


	REGIS_CLIENT_HANDLER(PB_VOICE_INFORM_HC, handleVoiceInform2Client);

	REGIS_CLIENT_HANDLER(PB_CHANGE_ACTOR_MODEL_HC, handleChangeActorModel2Client);

	REGIS_CLIENT_HANDLER(PB_NOTIFIY_MODEL_HC, handleNotifiyAvtarModel2Client);



	REGIS_CLIENT_HANDLER(PB_PLAYER_REVIVEPOINT_HC, handlePlayerRevivePoint2Client);

	//星站相关
	REGIS_CLIENT_HANDLER(PB_NOTIFY_STARSTATION_ADDED_HC, handleNotifyStarStationAdded2Client);
	REGIS_CLIENT_HANDLER(PB_NOTIFY_STARSTATION_REMOVED_HC, handleNotifyStarStationRemoved2Client);


	REGIS_CLIENT_HANDLER(PB_NOTIFY_ENTER_STARSTATION_CABIN_HC, handleNotifyEnterStarStationCabin2Client);


	REGIS_CLIENT_HANDLER(PB_NOTIFY_STARSTATION_CHANGENAMESTATUS_HC, handleNotifyStarStationChangeNameStatus2Client);

	REGIS_CLIENT_HANDLER(PB_NOTIFY_LEAVE_STARSTATION_CABIN_HC, handleNotifyLeaveStarStationCabin2Client);

	REGIS_CLIENT_HANDLER(PB_NOTIFY_UPGRADE_STARSTATION_CABIN_HC, handleNotifyUpgradeStarStationCabin2Client);


	REGIS_CLIENT_HANDLER(PB_NOTIFY_UPDATE_STARSTATION_CABIN_STATUS_HC, handleNotifyUpdateStarStationCabinStatus2Client);


	REGIS_CLIENT_HANDLER(PB_NOTIFY_UPDATE_STARSTATION_CABIN_STATUSEND_HC, handleNotifyUpdateStarStationStatusEnd2Client);


	REGIS_CLIENT_HANDLER(PB_NOTIFY_UPDATE_STARSTATION_CABIN_ADDED_HC, handleNotifyUpdateStarStationCabinAdded2Client);

	REGIS_CLIENT_HANDLER(PB_NOTIFY_UPDATE_STARSTATION_CABIN_REMOVED_HC, handleNotifyUpdateStarStationCabinRemoved2Client);



	REGIS_CLIENT_HANDLER(PB_NOTIFY_ADD_UNFINISHED_TRANSFER_RECORD_HC, handleNotifyAddUnfinishedTransferRecord2Client);

	REGIS_CLIENT_HANDLER(PB_NOTIFY_UPDATE_UNFINISHED_TRANSFER_RECORD_STATUS_HC, handleNotifyUpdateUnfinishedTransferRecordStatus2Client);


	REGIS_CLIENT_HANDLER(PB_NOTIFY_REMOVE_UNFINISHED_TRANSFER_RECORD_HC, handleNotifyRemoveUnfinishedTransferRecord2Client);

	REGIS_CLIENT_HANDLER(PB_STARSTATION_DATA_HC, handleStarStationData2Client);



	REGIS_CLIENT_HANDLER(PB_NOTIFY_PLAYER_TRANSFER_BY_STRSTATION_HC, handleNotifyStarStationTransfer2Client);
	REGIS_CLIENT_HANDLER(PB_NOTIFY_ACTIVATE_STARSTATION_HC, handleNotifyActivateStarStation2Client);
	REGIS_CLIENT_HANDLER(PB_NOTIFY_UPDATE_STARSTATION_SIGN_INFO_HC, handleNotifyUpdateStarStationSignInfo2Client);
	REGIS_CLIENT_HANDLER(PB_PLAYER_TRANSFER_HC, handleNotifyPlayerTransfer2Client);
	REGIS_CLIENT_HANDLER(PB_NOTIFY_PLAYER_BLOCK_CHANGE_COLOR_ANIM_HC, handleNotifyModBlockChangeColor2Client);

	REGIS_CLIENT_HANDLER(PB_NOTIFY_STARSTATION_TRANSFER_RESULT_HC, handleNotifyStarStationResult2Client);
	REGIS_CLIENT_HANDLER(PB_SYNC_PLAYER_POS_HC, handleSyncPlayerPos2Client);

	REGIS_CLIENT_HANDLER(PB_NOTIFY_PLAYALTMANMUSIC_HC, handlePlayAltmanMusic2Client);
	REGIS_CLIENT_HANDLER(PB_ACHIEVEMENT_SYNC_HC, handleAchievementUpdate2Client);
	REGIS_CLIENT_HANDLER(PB_ACHIEVEMENT_INITDATA_HC, handleAchievementInit2Client);


	REGIS_CLIENT_HANDLER(PB_NOTIFY_UPDATE_TOOL_MODEL_TEXTURE_HC, handleNotifyUpdateToolModelTexture2Client);

	// 20211027 广告商人购买 codeby：liusijia
	REGIS_CLIENT_HANDLER(PB_BUY_AD_SHOP_GOOD_HC, handleAdShopBuy2Client);

	// 主机通知客机星星币添加/扣除成功
	REGIS_CLIENT_HANDLER(PB_ADDEXPRESULT_HC, handleNewAdNpcAddExpResult2Client);
	REGIS_CLIENT_HANDLER(PB_SYNC_ROOM_EXTRA_HC, handleRoomExtra2Client);

	REGIS_CLIENT_HANDLER(PB_BATTLEPASS_EVENT_HC, handleBattlePassEvent2Client);

	REGIS_CLIENT_HANDLER(PB_HORSE_FLAG_HC, handleHorseFlag2Client);



	REGIS_CLIENT_HANDLER(PB_SET_TIANGOU_HC, handleSetTiangou2Client);

	REGIS_CLIENT_HANDLER(PB_PLAYER_OPENUI_HC, handlePlayerOpenUI2Client);

	// 20210910：坐骑隐身技能  codeby： keguanqiang
	REGIS_CLIENT_HANDLER(PB_RIDE_INVISIBLE_HC, handleRideInvisible2Client);


	//20210824： 主机通知客机道具交换成功 codeby: wangyu
	REGIS_CLIENT_HANDLER(PB_EXCHANGEITEMSTOBACKPACKRESULT_HC, handleExchangeItemsToBackPackResult2Client);

	//20210823 QQ音乐播放器 codeby:zoulongjin
	REGIS_CLIENT_HANDLER(PB_CHANGE_QQMUSIC_PLAYER_HC, handleChangeQQMusicPlayer2Client);

	//20210926 miniClub音乐 codeby:wangshuai
	REGIS_CLIENT_HANDLER(PB_MINICLUB_PLAYER_HC, handleMiniClubPlayer2Client);

	REGIS_CLIENT_HANDLER(PB_CUSTOM_MSG, handleCustomMsg2Client);

	//家园祈福
	REGIS_CLIENT_HANDLER(PB_HOME_PRAY_INFO_HC, handlePrayInfoClient);
	REGIS_CLIENT_HANDLER(PB_HOME_PRAY_TREE_STATE_HC, handlePrayTreeStageClient);
	REGIS_CLIENT_HANDLER(PB_HOME_PRAY_TIMEUPDATE_HC, handlePrayTimeClient);
	REGIS_CLIENT_HANDLER(PB_HOME_PRAY_REQ_HC, handlePrayReqClient);

	REGIS_CLIENT_HANDLER(PB_OPEN_HOMECLOSET_HC, handleOpenHomeCloset2Client);


	REGIS_CLIENT_HANDLER(PB_HOMELAND_RANCH_FOODERSTATE_HC, handleHomelandRanchFooderStateClient);
	REGIS_CLIENT_HANDLER(PB_HOMELAND_RANCH_HC, handleHomelandRanchInfo2Client);


	// 20210709：修改购买菜谱主客机同步  codeby： hyy
	REGIS_CLIENT_HANDLER(PB_HOMELAND_COOK_MENUBUY_HC, handleHomeLandMenuBuySuccess2Client);

	REGIS_CLIENT_HANDLER(PB_HOMELAND_FARM_SHOP_HC, handleHomeLandShopCell2Client);

	// 20210724：修改购买特惠家具主客机同步  codeby: yangzhenyu
	REGIS_CLIENT_HANDLER(PB_HOMELAND_COOK_SPFURNITUREBUY_HC, handleHomeLandSpecialFurnitureBuySuccess2Client);

	//20211020 注册喷漆同步事件 codeby : 柯冠强
	REGIS_CLIENT_HANDLER(PB_ADD_PAINTED_INFO_HC, handleAddPaintedInfo2Client);
	REGIS_CLIENT_HANDLER(PB_REMOVE_PAINTED_INFO_HC, handleRemovePaintedInfo2Client);

	//20211101 手持物品 codeby:luoshuai
	REGIS_CLIENT_HANDLER(PB_Equip_Weapon_HC, handlePlayerEquipWeapon2Client);
	
	//20210915 音乐方块 codeby:huangxin
	REGIS_CLIENT_HANDLER(PB_CHANGE_QQMUSIC_CLUB_HC, handleChangeQQMusicClub2Client);

	REGIS_CLIENT_HANDLER(PB_TOP_BRAND_HC, handleShowTopBrand2Client);

	//20220704 武器皮肤 同步熟练度 codeby:汪宇
	REGIS_CLIENT_HANDLER(PB_WEAPON_POINT_HC, handleWeaponPoint2Client);

	//20221016 闪电链 codeby:shitengkai
	REGIS_CLIENT_HANDLER(PB_ADDLIGHTCHAIN_HC, handleAddLightningChain2Client);
	//20221118 钓鱼 codeby shitengkai
	REGIS_CLIENT_HANDLER(PB_STARTFISHING_HC, handleStartFishing2Client);
	REGIS_CLIENT_HANDLER(PB_ENDFISHING_HC, handleEndFishing2Client);
	REGIS_CLIENT_HANDLER(PB_QUITFISHING_HC, handleQuitFishing2Client);
	REGIS_CLIENT_HANDLER(PB_CHANGEFISHINGSTAGE_HC, handleChangeFishingState2Client);
	REGIS_CLIENT_HANDLER(PB_FISHING_BEGIN_FLASH_HC, handleFishingBeginFlash2Client);
	//20221212 actor身上绑item显示 shitengkai
	REGIS_CLIENT_HANDLER(PB_PLAYER_VEHICLE_MOVEINPUT_HC, handlePlayerVehicleMoveInput2Client);
	REGIS_CLIENT_HANDLER(PB_BIND_ITEM_TO_ACTOR_HC, handleBindItemToActor2Client); 
	
	//20220809 联机房间 地图模式改变通知（创造/冒险互转） codeby:huangrulin
	REGIS_CLIENT_HANDLER(PB_GAME_MODE_CHANGE, handleGameModeChange2Client);

	REGIS_CLIENT_HANDLER(PB_PUSHSNOWBALL_OPERATE_HC, handlePushSnowBallOperate2Client);

	REGIS_CLIENT_HANDLER(PB_ACTOR_PLAY_ANIM_HC, handlePlayAnimation2Client);

	REGIS_CLIENT_HANDLER(PB_SYNC_MOVE_HC, handleSyncMove2Client);

	REGIS_CLIENT_HANDLER(PB_UIDISPLAYHORSE_HC, handleUIDisplayHorse2Client);

	// 2023年12月8日18:13:28 绑定player 到物理平台
	REGIS_CLIENT_HANDLER(PB_BIND_PLAYER_TO_PHYSICS_PLAT_HC, handleBindPlayerToPhysicsPlat2Client);
	REGIS_CLIENT_HANDLER(PB_UNBIND_PLAYER_TO_PHYSICS_PLAT_HC, handleUnBindPlayerToPhysicsPlat2Client);
	REGIS_CLIENT_HANDLER(PB_PHYSICS_COM_UPDATE, handlePhysicsComUpdate2Client);
	REGIS_CLIENT_HANDLER(PB_PHYSICS_COM_PLAT_LOCAL_POS, handlePhysicsComPlatLocPos2Client);
	REGIS_CLIENT_HANDLER(PB_EFFECT_COM_PARTICLE_UPDATE, handleEffctComUpdate2Client);
	REGIS_CLIENT_HANDLER(PB_SOUND_COM_UPDATE, handleSoundComUpdate2Client);
	REGIS_CLIENT_HANDLER(PB_METEOR_SHOWER_HC, handleMeteorShower2Client);
	//同步actor的参数
	REGIS_CLIENT_HANDLER(PB_SEND_OBJACTOR_MSG, handleObjActorMsg2Client);
	//同步弹孔信息
	REGIS_CLIENT_HANDLER(PB_ADD_BULLETHOLE_HC, handleAddBullethole2Client);

	REGIS_CLIENT_HANDLER(PB_PLAYER_CANFIRE_HC, handlePlayerCanFire2Client);

	REGIS_CLIENT_HANDLER(PB_ACTOR_PLAYANIM_NEW_CH, handleActorPlayAnimByIdNew2Client);
	
	//科技树
	REGIS_HOST_HANDLER(PB_OPEN_WORKBENCH_CH, handleOpenWorkbench2Host);
	REGIS_CLIENT_HANDLER(PB_OPEN_WORKBENCH_HC, handleOpenWorkbench2Client);
	REGIS_HOST_HANDLER(PB_UNLOCK_TECH_NODE_CH, handleUnlockTechNode2Host);
	REGIS_CLIENT_HANDLER(PB_UNLOCK_TECH_NODE_HC, handleUnlockTechNode2Client);

	REGIS_HOST_HANDLER(PB_PLAYER_CUSTOM_CH, handlePlayerCustom2Host);
	REGIS_CLIENT_HANDLER(PB_PLAYER_CUSTOM_HC, handlePlayerCustom2Client);

	REGIS_HOST_HANDLER(PB_DECOMPOSITION_CH, handleDecomposition2Host);
	REGIS_CLIENT_HANDLER(PB_DECOMPOSITION_HC, handleDecomposition2Client);

	REGIS_HOST_HANDLER(PB_AllSingleBuildData_CH, handleAllSingleBuildData2Host);
	REGIS_CLIENT_HANDLER(PB_AllSingleBuildData_HC, handleAllSingleBuildData2Client);

	//空投事件
	REGIS_CLIENT_HANDLER(PB_AIRDROP_EVENT_HC, handleAirDropEvent2Client);
	//查询空投宝箱
	REGIS_HOST_HANDLER(PB_AIRDROP_GET_CHEST_CH, handleGetAirDropChest2Host);
	REGIS_CLIENT_HANDLER(PB_AIRDROP_GET_CHEST_HC, handleGetAirDropChest2Client);
	//删除空投宝箱
 	REGIS_CLIENT_HANDLER(PB_AIRDROP_DEL_CHEST_HC, handleDelAirDropChest2Client);
	
	REGIS_HOST_HANDLER(PB_Research_CH, handleResearch2Host);
	REGIS_CLIENT_HANDLER(PB_Research_HC, handleResearch2Client);

	REGIS_HOST_HANDLER(PB_TechBlueprint_CH, handleTechBlueprint2Host);
	REGIS_CLIENT_HANDLER(PB_TechBlueprint_HC, handleTechBlueprint2Client);
}

void MpGameSurviveNetHandler::tick()
{
	GameSuviveNetHandler::tick();

	PlayerControl *playerCtrl = m_root->getPlayerControl();
	if (!playerCtrl)
	{
		ErrorStringMsg("MpGameSurviveNetHandler::tick playerCtrl is nil");
		return;
	}
	if (!m_RequestChunks.empty() && playerCtrl->getWorld() && m_CurWaitChunk.x == MIN_INT)
	{
		//(studio地图当玩家坐载具走的过快区块传输跟不上)优先传送距离本人最近的区块
		//m_CurWaitChunk = m_RequestChunks.front();
		int maxcount = 2048;//避免过量运算
		auto pchunk = m_RequestChunks.begin();
		m_CurWaitChunk = *m_RequestChunks.begin();
		WCoord center = CoordDivSection(playerCtrl->getPosition());
		auto remove = pchunk;
		while (pchunk != m_RequestChunks.end())
		{
			if ((abs(m_CurWaitChunk.x - center.x) + abs(m_CurWaitChunk.z - center.z)) 
             > (abs(pchunk->x - center.x) + abs(pchunk->z - center.z)))
			{
				m_CurWaitChunk.x = pchunk->x;
				m_CurWaitChunk.z = pchunk->z;
				remove = pchunk;
			}
			pchunk++;
			maxcount--;
			if (maxcount < 0)
				break;
		}
		if (remove != m_RequestChunks.end())
		{
			m_RequestChunks.erase(remove);
		}
		//m_RequestChunks.pop_front();

		do
		{
			///w%lld.%d_%d_%d.md5
			std::string trunk_md5;
			bool findTrunkSave = false;
			if (playerCtrl)
			{
				char path[256];
				sprintf(path, "data/cachetrunk/%d", (unsigned int)((playerCtrl->getOWID() >> 32) + (playerCtrl->getOWID() & 0xffff)*(playerCtrl->getOWID() & 0xffff) + (m_CurWaitChunk.x*m_CurWaitChunk.x) + m_CurWaitChunk.z) % CIOCMD_SAVE_CACHE_NUM);//playerCtrl->getOWID(), playerCtrl->getCurMapID()
				//,m_CurWaitChunk.x, m_CurWaitChunk.z);
				OneLevelScaner scaner;
				scaner.setRoot(GetFileManager().GetWritePathRoot());
				scaner.scanTree(path, 1);
				for (std::vector<std::string>::iterator it = scaner.m_FileNamesVec.begin(); it != scaner.m_FileNamesVec.end(); it++)
				{
					std::string filename = it->c_str();
					long long  owid = 0;
					int mapid = 0;
					int chunk_x = 0;
					int chunk_y = 0;
					int i = 1;
					std::string temp_;
					for (; i < (int)filename.size(); i++)
					{
						if (filename[i] != '_')
						{
							temp_ += filename[i];
						}
						else
						{
							break;
						}
					}
					std::stringstream sstr;
					sstr << temp_;
					sstr >> owid;
					if (owid != playerCtrl->getOWID())
					{
						core::string fullpath;
						core::string newPath(path);
						GetFileManager().ToWritePathFull(newPath, fullpath);
						fullpath += "/";
						fullpath += filename;
						DirVisitor::deleteFile(fullpath.c_str());
						continue;
					}
					if (findTrunkSave)
						continue;
					sstr.clear();
					temp_.clear();
					int i_ = 0;
					i++;
					for (; i < (int)filename.size(); i++)
					{
						if (filename[i] != '_')
						{
							temp_ += filename[i];
						}
						else
						{
							if (i_ == 0)
								mapid = atoi(temp_.c_str());
							else if (i_ == 1)
								chunk_x = atoi(temp_.c_str());
							else
								chunk_y = atoi(temp_.c_str());
							if (i_ >= 2)
							{
								i++;
								break;
							}
							i_++;
							temp_.clear();
						}
					}
					temp_.clear();
					if (mapid != playerCtrl->getCurMapID() || m_CurWaitChunk.x != chunk_x || m_CurWaitChunk.z != chunk_y)
					{
						continue;
					}

					for (; i < (int)filename.size(); i++)
					{
						if (filename[i] != '_')
						{
							trunk_md5 += filename[i];
						}
					}

					if (trunk_md5.size() != 32)
					{
						break;
					}

					findTrunkSave = true;
					//break;
				}
			}

			PB_SyncChunkDataCH syncChunkDataCH;
			PB_Vector3* sectionCoord = syncChunkDataCH.mutable_sectioncoord();
			sectionCoord->set_x(m_CurWaitChunk.x);
			sectionCoord->set_z(m_CurWaitChunk.z);
			sectionCoord->set_y(playerCtrl->getCurMapID());
			if (findTrunkSave)
			{
				unsigned char md5_out[16];
				encode32To16(trunk_md5.c_str(), md5_out);
				syncChunkDataCH.set_trunkmd5((const char *)md5_out, 16);
				m_CurWaitChunkMd5 = trunk_md5;
			}

			GetGameNetManagerPtr()->sendToHost(PB_SYNC_CHUNK_DATA_CH, syncChunkDataCH);
		} while (0);
	}

}

void MpGameSurviveNetHandler::onPlayerLeave(int uin)
{
	GetICloudProxyPtr()->SimpleSLOG("MpGameSurviveNetHandler::onPlayerLeave uin:%d", uin);

#ifndef IWORLD_SERVER_BUILD
	// 玩家离开房间埋点
	GameAnalytics::TrackEvent("player_leave_room", {
		{"uin", GameAnalytics::Value(uin)},
		{"host_uin", GameAnalytics::Value(GetGameNetManagerPtr() ? GetGameNetManagerPtr()->getHostUin() : 0)},
		{"room_host_type", GameAnalytics::Value(GetGameInfoProxy()->GetRoomHostType())}
	});
#endif

	MpGameSurvive* mp = dynamic_cast<MpGameSurvive*>(m_root);
	if (mp)
	{
		mp->onPlayerLeave(uin);
#ifdef IWORLD_SERVER_BUILD
		DataHubService::GetInstance().OnPlayerLeave(uin);
#endif
	}

	auto netMgr = GetGameNetManagerPtr();
	if (netMgr)
	{
		netMgr->onPlayerLeave(uin);
#ifndef IWORLD_SERVER_BUILD
		netMgr->kickoffUin(uin, true);
#endif
	}

	WorldManager* worldMgr = m_root->getWorldMgr();
	if (worldMgr)
	{
		worldMgr->checkNoPlayerToExit();
	}

	MNSandbox::GlobalNotify::GetInstance().NotifyHostClientLeaveRoom(uin);
}


void MpGameSurviveNetHandler::sendChat(const char *content, int type/* =0 */, int targetuin /* = 0 */, int language/* =1 */, const char* extend/*=""*/)
{
	ClientWorldContainer * container = NULL;
	MpGameSurvive*mp = dynamic_cast<MpGameSurvive*>(m_root);
	if (mp)
	{
		container = mp->getClientWorldContainer();
	}

#if GM_PROFILER
	if (content[0] == '/' && IsOpenCmd())
	{
		if (g_pPlayerCtrl)
		{
			g_pPlayerCtrl->execCmd(content + 1);
			return;
		}
	}
#endif

	std::string tmpContent = content;
	std::string tmpTranslate;
#ifdef IWORLD_UNIVERSE_BUILD
	SandboxResult sandboxResult = SandboxCoreDriver::GetInstance().GetManagers().GetEventDispatcherMgr().Emit("WorldStringTranslateMgr_splitProtoContent", SandboxContext(nullptr)
		.SetData_String("oldVal", tmpContent));
	if (sandboxResult.IsExecSuccessed())
	{
		tmpContent = sandboxResult.GetData_String("oldVal");
		tmpTranslate = sandboxResult.GetData_String("translate");
	}
#endif 


	if (content[0] != 0 || (container && (container->getBaseIndex() == SIGNS_START_INDEX)))
	{
		if (IsOpenCmd() && content[0] == '/' && container->getBaseIndex() != SIGNS_START_INDEX)
		{
			if (ExecCmdOnClient(content + 1)) return;
		}

		bool hasFilter = false;
		bool hasDirtyWords = false;
		MINIW::ScriptVM::game()->callFunction("CheckFilterString", "sb>b", tmpContent.c_str(), true, &hasFilter);
		MINIW::ScriptVM::game()->callFunction("C_GetFilterScore", "sb>b", tmpContent.c_str(), true, &hasDirtyWords);
		if (hasFilter || hasDirtyWords)
		{
			return;
		}
		/**
		 * 主机的系统消息通过 PB_CHAT_HC 直接发送到客机(包括自己)
		 * 所有人(包括主机)的聊天消息通过 PB_CHAT_CH 发给主机后统一分发
		 * 客机不允许发送系统消息
		 * 2022.07.13 by huanglin
		 */
		if ((1 == type|| 5 == type) && g_WorldMgr && !g_WorldMgr->isRemote())
		{
			// 2021.12.28 云服调用此接口时直接发给客户端即可
			PB_ChatHC chatHC;
			chatHC.set_chattype(type);
			chatHC.set_content(tmpContent);
			chatHC.set_language(language);
			if (extend && extend[0])
				chatHC.set_extend(extend);
			
			chatHC.set_speaker("");
			chatHC.set_uin(0);
			if (chatHC.has_translate())
			{
				chatHC.set_translate(tmpTranslate);
			}
			if (targetuin)
				GetGameNetManagerPtr()->sendToClient(targetuin, PB_CHAT_HC, chatHC, 0, false);
			else
				GetGameNetManagerPtr()->sendBroadCast(PB_CHAT_HC, chatHC, 0, false);
		}
		else
		{
			sendChatCH(tmpContent.c_str(), type, targetuin, language, extend, tmpTranslate);
		}
	}
	else
	{
		PlayerControl *playerCtrl = m_root->getPlayerControl();
		GetGameEventQue().postChatEvent(type, NULL, tmpContent.c_str(), playerCtrl ? playerCtrl->getUin() : 0, language);
	}
}


void MpGameSurviveNetHandler::sendChatCH(const char* content, int type, int targetuin, int language, const char* extend, const std::string& tmpTranslate)
{
	PB_ChatCH chatCH;
	chatCH.set_chattype(type);
	chatCH.set_targetuin(targetuin);
	chatCH.set_content(content);
	chatCH.set_language(language);
	if (extend)
	{
		chatCH.set_extend(extend);
	}
	if (!tmpTranslate.empty())
	{
		chatCH.set_translate(tmpTranslate);
	}
#if !defined(IWORLD_SERVER_BUILD) && !defined(IWORLD_UNIVERSE_BUILD)
	std::string addParamStr;
	auto L = MINIW::ScriptVM::game()->getLuaState();
	if (ScriptVM::isCurrentThreadIsMainThread(L))
	{
		int top = lua_gettop(L);
		lua_getglobal(L, "url_addParams");
		if (lua_isfunction(L, -1))
		{
			lua_pushstring(L, "");
			if (0 == lua_pcall(L, 1, 1, 0))// 调用函数，1个参数，1个返回值
			{
				if (lua_isstring(L, -1)) {
					// 获取字符串及其长度
					size_t len;
					const char* luaStr = lua_tolstring(L, -1, &len);
					addParamStr = luaStr;
				}
			}
		}
		lua_settop(L, top);

		if (!addParamStr.empty() && (addParamStr[0] == '&' || addParamStr[0] == '?'))
		{
			addParamStr.erase(0, 1);
		}
	}
	chatCH.set_wwparam(addParamStr);

#elif defined(IWORLD_SERVER_BUILD)
	chatCH.set_wwtk1("CloudHost");
	chatCH.set_wwtk2(m_lifeToken);
	chatCH.set_wwparam("");
#endif
	GetGameNetManagerPtr()->sendToHost(PB_CHAT_CH, chatCH);
}

void MpGameSurviveNetHandler::handleRClickUpInteract2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = checkPlayerByMsg2Host(uin);
	if (player == NULL)
	{
		return;
	}
	PB_RClickUpInteractCH rClickUpInteractCH;
	rClickUpInteractCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientActor* target = player->getWorld()->getActorMgr()->ToCastMgr()->findActorByWID(rClickUpInteractCH.target());
	if (target == NULL)
	{
		return;
	}
	target->rightClickUpInteract(player);
}

void MpGameSurviveNetHandler::handlePCMouseEvent2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = checkPlayerByMsg2Host(uin);
	if (player == NULL)
	{
		return;
	}
	PB_PCMouseEventCH event;
	event.ParseFromArray(pkg.MsgData, pkg.ByteSize);
	player->mouseEventTrigger(event.keytype(), event.eventtype());
}


void MpGameSurviveNetHandler::handleCraftQueueAddTask2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = checkPlayerByMsg2Host(uin);
	if (player == NULL)
	{
		return;
	}
	PB_CraftingQueueAddTaskCH ch;
	ch.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	auto craftQueue = player->getCraftingQueue();
	if (craftQueue != nullptr)
	{
		const CraftingDef* craDef = GetDefManagerProxy()->getCraftingDef(ch.crafting_id());
		if (!craDef)
		{
			LOG_WARNING("Invalid  craDef id :", ch.crafting_id());
		}
		int timeTick = craDef->CookingTick;
		if (0 == timeTick)
		{
			timeTick = 400;//todo  //配置0 改成400
		}
		if (ch.count() <=0)//非法数据
		{
			return;
		}
		craftQueue->addTask(ch.crafting_id(), ch.count(), timeTick);
	}

}

void MpGameSurviveNetHandler::handleCraftQueueRemoveTask2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = checkPlayerByMsg2Host(uin);
	if (player == NULL)
	{
		return;
	}
	PB_CraftingQueueRemoveTaskCH ch;
	ch.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	auto craftQueue = player->getCraftingQueue();
	if (craftQueue != nullptr)
	{
		craftQueue->removeTask(ch.task_index());
	}
}

void MpGameSurviveNetHandler::handleCraftQueueSwapTask2Host(int uin, const PB_PACKDATA& pkg)
{
	ClientPlayer* player = checkPlayerByMsg2Host(uin);
	if (player == NULL)
	{
		return;
	}
	PB_CraftingQueueSwapTaskCH ch;
	ch.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	auto craftQueue = player->getCraftingQueue();
	if (craftQueue != nullptr)
	{

	}
}

//ClientPlayer *MpGameSurviveNetHandler::uin2Player(int uin)
//{
//	WorldManager *worldMgr = m_root->getWorldMgr();
//	if (worldMgr)
//	{
//		return worldMgr->getPlayerByUin(uin);
//	}
//	return NULL;
//}


void MpGameSurviveNetHandler::kickoff(int uin)
{
	LOG_INFO("MpGameSurviveNetHandler:MpGameSurvive::kickoff %d", uin);
#ifndef DEDICATED_SERVER
	onPlayerLeave(uin);
#else
	auto netMgr = GetGameNetManagerPtr();
	if (netMgr)
	{
		netMgr->kickoffUin(uin, true);
	}
#endif
	//MNSandbox::GlobalNotify::GetInstance().NotifyHostClientLeaveRoom(uin);
}

void MpGameSurviveNetHandler::applyPermits(int uin)
{
	PB_JruisdicTionCH jruisdicTionCH;
	jruisdicTionCH.set_uin(uin);

	GetGameNetManagerPtr()->sendToHost(PB_ROOM_JRUISDICTION_CH, jruisdicTionCH);
}

void MpGameSurviveNetHandler::replyApplyPermits(int uin, int ret)
{
	PB_JruisdicTionHC jruisdicTionHC;
	jruisdicTionHC.set_ret(ret);

	GetGameNetManagerPtr()->sendToClient(uin, PB_ROOM_JRUISDICTION_HC, jruisdicTionHC);
}

void MpGameSurviveNetHandler::clientLogin(long long objId, const PB_OWGlobal &worldGlobal, const PB_RoleData &roleData)
{
	//LOG_INFO("MpGameSurvive::clientLogin(): objId = %d", objId);
	World *pworld = NULL;
	WorldManager *worldMgr = m_root->getWorldMgr();
	PlayerControl *playerCtrl = m_root->getPlayerControl();
	{
		if(worldMgr == NULL) return;
		if(playerCtrl == NULL) return;

		worldMgr->loadGlobal(worldGlobal, true);
		playerCtrl->reStoreRoleData(roleData);
		WCoord playerpos = playerCtrl->getPosition();
		pworld = worldMgr->createWorld(playerCtrl->getCurMapID());
		worldMgr->m_RenderEyeMap = playerCtrl->getCurMapID();
		playerCtrl->setPosition(playerpos.x, playerpos.y, playerpos.z);
	}
    if(pworld == NULL) 
		return;

	if(playerCtrl) 
		playerCtrl->SetObjId(objId);

	pworld->getActorMgr()->ToCastMgr()->spawnPlayerAddRef(playerCtrl);
	GetGameEventQue().postBackpackChange(-1);

#if OGRE_PLATFORM == OGRE_PLATFORM_WIN32

#else
	MpGameSurvive*mp = dynamic_cast<MpGameSurvive*>(m_root);
	if (mp)
	{
		mp->setOperateUI(false);
	}
#endif

}

void MpGameSurviveNetHandler::sendMsgClientEnterHostWorld()
{
	PlayerControl *playerCtrl = m_root->getPlayerControl();
	// TODO: 由于之前直接向主机发送请求进入房间的协议[OW_ROLE_ENTER_WORLD_CH]无法正常发出，
// 加了4条心跳消息后即可正常发送，目前未找出具体原因，估计与tdr协议打包有关系
	for (int i = 0; i < 4; i++)
	{
		PB_HeartBeatCH heartBeat;
		heartBeat.set_beatcode(0);
		GetGameNetManagerPtr()->sendToHost(PB_HEARTBEAT_CH, heartBeat);
	}
	MINIW::ScriptVM::game()->callString("if UploadClientInfoToCloudServer then UploadClientInfoToCloudServer() end");
	PB_RoleEnterWorldCH roleEnterWorldCH;
	roleEnterWorldCH.set_uin(GetClientInfoProxy()->getUin());
	roleEnterWorldCH.set_geniuslv(GetClientInfoProxy()->getGenuisLv(GetClientInfoProxy()->getAccountInfo()->RoleInfo.Model));
	roleEnterWorldCH.set_uictrlmode((playerCtrl && playerCtrl->isPCControl()) ? 1 : 0);
	roleEnterWorldCH.set_lang(GetClientInfoProxy()->getGameData("lang"));
	roleEnterWorldCH.set_apiid(GetClientInfoProxy()->GetAppId());
	roleEnterWorldCH.set_reserved(1);

	PB_RoleInfo* roleInfo = roleEnterWorldCH.mutable_roleinfo();
	roleInfo->set_model(GetClientInfoProxy()->getAccountInfo()->RoleInfo.Model);

	// 客户端自行检测，防止随意修改
	int skinId = GetClientInfoProxy()->getAccountInfo()->RoleInfo.SkinID;
	bool canUse = false;
	MINIW::ScriptVM::game()->callFunction("CheckSkinCanUse", "i>b", skinId, &canUse);
	if (!canUse)
	{
		skinId = 0;
	}
	roleInfo->set_skinid(skinId);

	roleInfo->set_nickname(GetClientInfoProxy()->getNickName());
	roleInfo->set_frameid(GetClientInfoProxy()->getHeadFrameId());
	char customskins = GetClientInfoProxy()->getAccountInfo()->RoleInfo.CustomSkin[0];
	if (customskins && skinId == 0)
	{
		GetCoreLuaDirector().CallFunction("ChangeAvtarBodyModel", "is>i", GetClientInfoProxy()->getUin(), GetClientInfoProxy()->getAccountInfo()->RoleInfo.CustomSkin, &skinId);
		MINIW::ScriptVM::game()->callFunction("CheckSkinCanUse", "i>b", skinId, &canUse);
		if (skinId > 0 && canUse)
		{
			roleInfo->set_skinid(skinId);
		}
		roleInfo->set_customjson(GetClientInfoProxy()->getAccountInfo()->RoleInfo.CustomSkin);
	}

	PB_PlayerVipInfo* vipInfo = roleEnterWorldCH.mutable_vipinfo();
	vipInfo->set_viptype(GetClientInfoProxy()->getAccountVipInfo().vipType);
	vipInfo->set_viplevel(GetClientInfoProxy()->getAccountVipInfo().vipLevel);
	vipInfo->set_vipexp(GetClientInfoProxy()->getAccountVipInfo().vipExp);

	// 仅对云服主机上传认证字段 2022.06.21 by huanglin
	if (ROOM_SERVER_RENT == GetClientInfoProxy()->getRoomHostType())
	{
		// 2021-12-17 codeby:liusijia  发送客户端sign到服务器检测
		std::string sign, s2t;
		GetDefManagerProxy()->getS2(sign, s2t);
		char authbuf[256];
		unsigned int curtimestamp = MINIW::GetTimeStamp();
		sprintf(authbuf, "%s%u", sign.c_str(), curtimestamp);
		string auth = gFunc_getmd5(authbuf);

		memset(authbuf, 0, sizeof(authbuf));
		sprintf(authbuf, "%s|%s|%s|%u", auth.c_str(), sign.c_str(), s2t.c_str(), curtimestamp);

		roleEnterWorldCH.set_auth(authbuf);
	}

	std::string sessionId = "";
	MNSandbox::GetGlobalEvent().Emit<std::string&>("HttpReportMgr_getGameSessionId", sessionId);
	roleEnterWorldCH.set_game_session_id(sessionId.c_str());

	//活动组队-队伍ID
	int specifyTeam = 0;
	MINIW::ScriptVM::game()->callFunction("GetSpecifyTeam", ">i", &specifyTeam);
	if (specifyTeam > 0) {
		roleEnterWorldCH.set_specify_team(specifyTeam);
	}
	
	// 悦享赛事称号 20230606 by wuyuwang
	char titlename[64] = { 0 };
	MINIW::ScriptVM::game()->callFunction("GetCurUseTitle", ">s", &titlename);
	if (0 != strlen(titlename)) {
		roleInfo->set_bptitle(titlename);
	}
	//
	{
		//if (MNSandbox::Config::GetSingleton().IsRemote()) {
			//客机上报版本号
			int uin = MNSandbox::Config::GetSingleton().GetLocalUin();
			unsigned v = MNSandbox::Config::ms_version;
			MNSandbox::RemoteMsg::GetSingleton().SendToHost(MNSandbox::SdbSceneManager::ms_globalServiceNodeid, "OnRecvClientSandboxVersion", uin, v);
		//}
	}

	if (ROOM_BY_PLAYER == GetClientInfoProxy()->getRoomHostType())
	{
		const RoomDesc* roominfo = GetRoomManagerPtr()->getIthRoom(0);
		if (roominfo)
		{
			GetClientInfo()->syncPlayerArch2Host(atoll(roominfo->map_type.c_str()));
		}
	}

	GetGameNetManagerPtr()->sendToHost(PB_ROLE_ENTER_WORLD_CH, roleEnterWorldCH, 0, RELIABLE_ORDERED, IMMEDIATE_PRIORITY);
	LOG_DEBUG("MpGameSurviveNetHandler::sendMsgClientEnterHostWorld PB_ROLE_ENTER_WORLD_CH");
}

void MpGameSurviveNetHandler::keepGame()
{
	unsigned int curtick = Rainbow::Timer::getSystemTick();
	if (isHost())
	{
#if !STUDIO_SERVER
		// windowsserver不触发超时
#ifndef WINDOWS_SERVER
			int outtime = 180;
#ifdef DEDICATED_SERVER
			outtime = Rainbow::GetICloudProxyPtr()->GetHeatbeatTime();
#endif
		auto& heartbeat =  GetGameNetManagerPtr()->m_HostRecvHeartBeart;
		for (auto iter = heartbeat.begin(); iter != heartbeat.end(); iter++)
		{
			int duration = curtick - iter->second;

			if (curtick > iter->second && duration >= outtime * 1000)
			{
				// 客户端提示超时
				GameNetManager::getInstance()->getConnection()->kickoffMember(iter->first, ERR_CODE_BREATH_TIMEOUT);
				SLOG(INFO) << "kick player by heartbeat timeout " << iter->first;
				heartbeat.erase(iter);
				break;
			}
		}
#endif
#endif
	}
	else if (isClient())
	{
		if (curtick - m_SendHeartBeatTick >= 10 * 1000)
		{
			PB_HeartBeatCH heartBeatCH;
			heartBeatCH.set_beatcode(curtick);
			heartBeatCH.set_server_time(m_RecvedServerTimeStamp);
			// 上报时间戳，用于服务器时间校验，防止客户端使用时间加速  codeby:liusijia 20211015
			if (m_RecvedServerTimeStamp == 0)
			{
				heartBeatCH.set_client_time(0);
			}
			else
			{
				unsigned int passTime = curtick - m_RecvBeatClientTimeStamp;
				heartBeatCH.set_client_time(m_RecvedServerTimeStamp + passTime);
			}

			// 每30次同步心跳时上报一次，约为5分钟一次
			static int CheckNeedSyncAce = 0;
			if (--CheckNeedSyncAce <= 0)
			{
				CheckNeedSyncAce = 30;
				// TODO(liusjia) 获取aceinfo
				/*std::string aceinfo = IMiniGameProxy::getMiniGameProxy()->getAceInfo();
				heartBeatCH.set_aceinfo(aceinfo);*/
			}

			bool b = GetGameNetManagerPtr()->sendToHost(PB_HEARTBEAT_CH, heartBeatCH);
			//LOG_INFO("GAMENET MPGameSurvice tick sendToHost:%s",b ? "true" :"false");

			m_SendHeartBeatTick = curtick;
		}
	}
}

// 添加递归遍历函数
static void addTechNodeRecursive(PB_TechTree* tree, TechNode* node) {
    // 添加当前节点
    PB_TechNode* pbNode = tree->add_treenodes();
    pbNode->set_id(node->Id);
    pbNode->set_preid(node->PreId); 
    pbNode->set_itemid(node->ItemId);
    pbNode->set_level(node->Level);
    pbNode->set_isunlocked(node->IsUnlocked);

    // 递归遍历所有子节点
    for (auto child : node->Children) {
        addTechNodeRecursive(tree, child);
    }
}


static void setPlayerWorkbenchTechNode(int uin, PB_OpenWorkbenchHC& protoHC, int rootTreeId, const miniw::tech_tree_node& user_node) {
	auto* trees = protoHC.mutable_techtreelist();
	for (auto& tree : *trees) {   
		if (tree.roottreeid() == rootTreeId) {
			auto* nodes = tree.mutable_treenodes();
			for (auto& node : *nodes) {   
				if (node.id() == user_node.id()) {
					// 直接修改原协议中的值
					node.set_isunlocked(user_node.is_unlocked());
					node.set_level(user_node.level());
					node.set_itemid(user_node.item_id());
					node.set_preid(user_node.pre_id());
				}
			}
		}
	}
}

// 检查节点是否解锁
static bool isUnLocked(miniw::tech_tree* tech_tree, int nodeId) {
	if (!tech_tree)
		return false;

	for (auto& node : tech_tree->tree_nodes()) {
		if (node.id() == nodeId) {
			return node.is_unlocked();
		}
	}

	return false;
}

// 获取科技树信息
void MpGameSurviveNetHandler::handleOpenWorkbench2Host(int uin, const PB_PACKDATA& pkg) {
	ClientPlayer *player = checkPlayerByMsg2Host(uin);
	if (player == NULL) {
		
		//返回协议
		PB_OpenWorkbenchHC protoHC;
		protoHC.set_ret(-1);
		protoHC.set_msg("player is NULL");
		GetGameNetManagerPtr()->sendToClient(uin, PB_OPEN_WORKBENCH_HC, protoHC);
		LOG_WARNING("handleOpenWorkbench2Host uin=%d player is NULL", uin);
		return;
	}

	PB_OpenWorkbenchCH protoCH;
	protoCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	int level = protoCH.level();
	LOG_INFO("handleOpenWorkbench2Host uin=%d level=%d", uin, level);

	std::vector<miniw::tech_tree*> playerTechTrees = player->getTechTrees(level); //从player获取玩家科技树
	std::vector<TechTree*> csvTechTrees = WorkbenchTechCsv::getInstance()->getWorkbenchTechTrees(level); //从csv获取科技树
    //返回协议
	PB_OpenWorkbenchHC protoHC;
	protoHC.set_ret(0);
	protoHC.set_msg("success");
	
	for (auto techTree : csvTechTrees) {
		// 添加技能树
		PB_TechTree* new_tree = protoHC.add_techtreelist(); 
		new_tree->set_roottreeid(techTree->RootId);
		new_tree->set_level(techTree->WorkbenchLevel);
		// 添加TreeNodes
		addTechNodeRecursive(new_tree, techTree->RootNode);
		// 检查玩家科技树是否存在
		std::vector<miniw::tech_tree*> playerTechTrees = player->getTechTrees(level,techTree->RootId);
		miniw::tech_tree* tree = playerTechTrees.size() > 0 ? playerTechTrees[0] : nullptr;
		if (tree) {
			 
			for (auto node : tree->tree_nodes()) {
				//用个人科技树数据，填充协议
				setPlayerWorkbenchTechNode(uin, protoHC, tree->root_tree_id(), node);
			}
		}
	}

	LOG_INFO("handleOpenWorkbench2Host uin=%d ret=%d msg=%s", uin, protoHC.ret(), protoHC.msg().c_str());

	GetGameNetManagerPtr()->sendToClient(uin, PB_OPEN_WORKBENCH_HC, protoHC);
}

void MpGameSurviveNetHandler::handleOpenWorkbench2Client(const PB_PACKDATA_CLIENT& pkg) {

	PlayerControl* playerCtrl = m_root->getPlayerControl();
	if (playerCtrl == NULL || playerCtrl->getWorld() == NULL) return;

	PB_OpenWorkbenchHC protoHC;
	protoHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	std::string json_str = "[";
	
	// 遍历TechTreeList并构建JSON
	for (int i = 0; i < protoHC.techtreelist_size(); i++) {
		const PB_TechTree& tree = protoHC.techtreelist(i);
		
		if (i > 0) {
			json_str += ",";
		}
		
		json_str += "{\"root_tree_id\":" + std::to_string(tree.roottreeid()) + ",\"level\":\"" + std::to_string(tree.level()) + "\",\"nodes\":[";
		
		// 遍历树中的节点
		for (int j = 0; j < tree.treenodes_size(); j++) {
			const PB_TechNode& node = tree.treenodes(j);
			
			if (j > 0) {
				json_str += ",";
			}
			
			json_str += "{\"id\":" + std::to_string(node.id()) + 
				",\"pre_id\":" + std::to_string(node.preid()) + 
				",\"item_id\":" + std::to_string(node.itemid()) + 
				",\"level\":" + std::to_string(node.level()) + 
				",\"is_unlocked\":" + (node.isunlocked() ? "true" : "false") + "}";
		}
		
		json_str += "]}";
	}
	
	json_str += "]";

	SandboxEventDispatcherManager::GetGlobalInstance().Emit("TechTree_openWorkbench",
		SandboxContext(nullptr)
		.SetData_Number("ret", protoHC.ret())
		.SetData_String("msg", protoHC.msg())
		.SetData_String("tech_trees", json_str)
	);
}

void MpGameSurviveNetHandler::handleUnlockTechNode2Host(int uin, const PB_PACKDATA& pkg) {
	LOG_INFO("handleUnlockTechNode2Host uin=%d", uin);

	ClientPlayer *player = checkPlayerByMsg2Host(uin);
	if (player == NULL) {
		//返回协议
		PB_UnlockTechNodeHC protoHC;
		protoHC.set_ret(-1);
		protoHC.set_msg("player is NULL");
		GetGameNetManagerPtr()->sendToClient(uin, PB_UNLOCK_TECH_NODE_HC, protoHC);
		LOG_WARNING("handleUnlockTechNode2Host uin=%d player is NULL", uin);
		return;
	}
	int all_num = player->getBackPack()->getItemCountInNormalPack(11);
	PB_UnlockTechNodeCH protoCH;
	protoCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	LOG_INFO("handleUnlockTechNode2Host uin=%d level=%d rootTreeId=%d nodeId=%d", uin, protoCH.level(), protoCH.roottreeid(), protoCH.nodeid());

	int level = protoCH.level();
	int rootTreeId = protoCH.roottreeid();
	int nodeId = protoCH.nodeid();

	// 返回协议
	int ret = 0;
	string msg = "success";
	PB_UnlockTechNodeHC protoHC;
	protoHC.set_level(level);
	protoHC.set_nodeid(nodeId);

	//检查当前节点是否已经解锁
	miniw::tech_tree_node* node = player->getTechTreeNode(level, nodeId);
	if (node && node->is_unlocked()) {
		protoHC.set_ret(0);
		protoHC.set_msg("already unlock");

		LOG_INFO("handleUnlockTechNode2Host uin=%d ret=%d msg=%s", uin, protoHC.ret(), protoHC.msg().c_str());

		//已经解锁
		GetGameNetManagerPtr()->sendToClient(uin, PB_UNLOCK_TECH_NODE_HC, protoHC);
		return;
	}

	// 获取科技树配置
	TechTree* csvTechTree = WorkbenchTechCsv::getInstance()->getWorkbenchTechTree(level);
	if (csvTechTree == NULL) {
 		protoHC.set_ret(-2);
		protoHC.set_msg("no find tech tree");
		LOG_WARNING("handleUnlockTechNode2Host uin=%d ret=%d msg=%s", uin, protoHC.ret(), protoHC.msg().c_str());
		GetGameNetManagerPtr()->sendToClient(uin, PB_UNLOCK_TECH_NODE_HC, protoHC);
		return;
		 
	}
	//获取科技树配置Node
	TechNode* csvTreeNode = WorkbenchTechCsv::getInstance()->getWorkbenchTechNode(level, nodeId);
	if (csvTreeNode == NULL) {
		protoHC.set_ret(-3);
		protoHC.set_msg("no find tech tree node");
		LOG_WARNING("handleUnlockTechNode2Host uin=%d ret=%d msg=%s",uin, protoHC.ret(), protoHC.msg().c_str());
		GetGameNetManagerPtr()->sendToClient(uin, PB_UNLOCK_TECH_NODE_HC, protoHC);
		return;
	}

	// 获取该节点下的所有父节点
	std::vector<TechNode*> csvAllPreeNode = WorkbenchTechCsv::getInstance()->getAllPreNodes(level, nodeId);
	// 获取玩家已经解锁的节点
	miniw::tech_tree* userTechTree = player->getTechTree(level);
	
	std::vector<TechNode*> allUnlockedPreNode;
	//找出所有未解锁的父节点
	for (auto child : csvAllPreeNode) {
		if (userTechTree==nullptr || !isUnLocked(userTechTree, child->Id)) {
			allUnlockedPreNode.push_back(child);
		}
	}

	// 检查是否可以解锁
	bool can_unlock = true;
	// 计算各种物品消耗量
	std::map<int, int> costmap;
	//1、先加入当前解锁节点的消耗
	costmap[csvTreeNode->CostItem] = csvTreeNode->CostValue; 

    //2、累加依赖节点的消耗
	if (allUnlockedPreNode.size() > 0) {
		 
		for (auto child : allUnlockedPreNode) {
			int itemid = child->CostItem;
			if (itemid == 0)
				continue;
			if (costmap.find(itemid) == costmap.end()) {
				costmap[itemid] = child->CostValue;
			}
			else {
				costmap[itemid] += child->CostValue;
			} 
		}
	}
	//3、检查消耗品是否足够
	for (auto it : costmap)
	{
		int itemid = it.first;
		int cost = it.second;
		//检查背包中物品数量是否足够，任务一个不够则不能解锁
		if ( player->getBackPack()->getItemCountInNormalPack(itemid) < cost ) {
			can_unlock = false;
			ret = -4;
			msg = "not enough item num";
			break;
		}
	}
	// 解锁节点
	if (can_unlock) {
		// 先解锁前置节点
		for (auto preNode : allUnlockedPreNode) {
			player->unlockTechNode(level, rootTreeId, preNode->Id);
		}
		// 再解锁目标节点
		player->unlockTechNode(level, rootTreeId, nodeId);
		
		// 消耗物品扣减
		for (auto it : costmap) {
			int itemid = it.first;
			int num = it.second;
			player->getBackPack()->removeItemInNormalPack(itemid, num);

			//在返回协议中设置物品数量
			int count = player->getBackPack()->getItemCountInNormalPack(itemid);
			CostItem* item = protoHC.add_itemnums();
			item->set_itemid(itemid);
			item->set_itemnum(count);
		}
	}

	// 返回协议
	// 返回协议
	if(can_unlock) {
		ret = 0;
		msg = "success";
	}
 
	protoHC.set_ret(ret);
	protoHC.set_msg(msg);
	 

	LOG_INFO("handleUnlockTechNode2Host uin=%d ret=%d msg=%s", uin, ret, msg.c_str());
	 
	GetGameNetManagerPtr()->sendToClient(uin, PB_UNLOCK_TECH_NODE_HC, protoHC);
}

void MpGameSurviveNetHandler::handleUnlockTechNode2Client(const PB_PACKDATA_CLIENT& pkg) {
	PlayerControl* playerCtrl = m_root->getPlayerControl();
	if (playerCtrl == NULL || playerCtrl->getWorld() == NULL) return;

	PB_UnlockTechNodeHC protoHC;
	protoHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	if (protoHC.itemnums_size() > 0) 
	{

		SandboxEventDispatcherManager::GetGlobalInstance().Emit("TechTree_unlockTechNode",
			SandboxContext(nullptr)
			.SetData_Number("ret", protoHC.ret())
			.SetData_String("msg", protoHC.msg())
			.SetData_Number("level", protoHC.level())
			.SetData_Number("nodeid", protoHC.nodeid())
			.SetData_Number("item_num", protoHC.itemnums().Get(0).itemnum())
		);
	}
	else 
	{

		SandboxEventDispatcherManager::GetGlobalInstance().Emit("TechTree_unlockTechNode",
			SandboxContext(nullptr)
			.SetData_Number("ret", protoHC.ret())
			.SetData_String("msg", protoHC.msg())
			.SetData_Number("level", protoHC.level())
			.SetData_Number("nodeid", protoHC.nodeid())
		);
	}
		
}

void MpGameSurviveNetHandler::handlePlayerCustom2Host(int uin, const PB_PACKDATA& pkg)
{
	PB_PlayerCustomCH protoCH;
	protoCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	ClientPlayer* clientplayer = uin2Player(uin);
	if (clientplayer)
	{
		clientplayer->OnPlayerCustomMessage(protoCH.type(), protoCH.data());
	}
}


void MpGameSurviveNetHandler::handleAirDropEvent2Client(const PB_PACKDATA_CLIENT& pkg) {
	PlayerControl* playerCtrl = m_root->getPlayerControl();
	if (playerCtrl == NULL || playerCtrl->getWorld() == NULL) return;

	PB_AirDrop_Event protoHC;
	protoHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	SandboxEventDispatcherManager::GetGlobalInstance().Emit("SocReviveEvent_AirDrop",
		SandboxContext(nullptr)
		.SetData_Number("eventid", protoHC.eventid())
		.SetData_String("eventname", protoHC.eventname())
		.SetData_String("eventtype", protoHC.eventtype())
		.SetData_Number("start_pos_x", protoHC.start_pos_x())
		.SetData_Number("start_pos_y", protoHC.start_pos_y())
		.SetData_Number("start_pos_z", protoHC.start_pos_z())
		.SetData_Number("end_pos_x", protoHC.end_pos_x())
		.SetData_Number("end_pos_y", protoHC.end_pos_y())
		.SetData_Number("end_pos_z", protoHC.end_pos_z())
		.SetData_Number("drop_pos_x", protoHC.drop_pos_x())
		.SetData_Number("drop_pos_y", protoHC.drop_pos_y())
		.SetData_Number("drop_pos_z", protoHC.drop_pos_z())
		.SetData_Number("current_pos_x", protoHC.current_pos_x())
		.SetData_Number("current_pos_y", protoHC.current_pos_y())
		.SetData_Number("current_pos_z", protoHC.current_pos_z())
		.SetData_Number("timestamp", protoHC.timestamp())
	);
	 
}


void MpGameSurviveNetHandler::handleGetAirDropChest2Host(int uin, const PB_PACKDATA& pkg) {
	PlayerControl* playerCtrl = m_root->getPlayerControl();
	if (playerCtrl == NULL || playerCtrl->getWorld() == NULL) return;
	ClientPlayer* player = uin2Player(uin);
	if (player == NULL) return;
	
	PB_AIRDROP_GetChestReq protoCH;
	protoCH.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	PB_AIRDROP_GetChestRsp protoHC;

	auto& chest_list = AirDropEvent::chestList;

	for (auto& chest : chest_list) {
		ChestItem* item = protoHC.add_list();
		item->set_eventid(chest.event_id);
		item->set_spawntime(chest.spawn_time);
		item->mutable_pos()->set_x(chest.pos.x);
		item->mutable_pos()->set_y(chest.pos.y);
		item->mutable_pos()->set_z(chest.pos.z);
	}	

	protoHC.set_ret(0);
	protoHC.set_msg("success");

	GetGameNetManagerPtr()->sendToClient(uin, PB_AIRDROP_GET_CHEST_HC, protoHC);

}

void MpGameSurviveNetHandler::handleGetAirDropChest2Client(const PB_PACKDATA_CLIENT& pkg) {
	PlayerControl* playerCtrl = m_root->getPlayerControl();
	if (playerCtrl == NULL || playerCtrl->getWorld() == NULL) return;
	PB_AIRDROP_GetChestRsp protoHC;
	protoHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	jsonxx::Array chestArray;
	for (int i = 0; i < protoHC.list_size(); i++) {
		ChestItem item = protoHC.list(i);
		int eventId = item.eventid();
		int spawnTime = item.spawntime();
		WCoord pos ;
		pos.x = item.pos().x();
		pos.y = item.pos().y();
		pos.z = item.pos().z();
		
		jsonxx::Object chestObj;
		chestObj << "eventId" << item.eventid();
		chestObj << "spawnTime" << item.spawntime();
		chestObj << "pos_x" << item.pos().x();
		chestObj << "pos_y" << item.pos().y();
		chestObj << "pos_z" << item.pos().z();
		chestArray << chestObj;
 	}

	std::string jsonstr = chestArray.json();

	SandboxEventDispatcherManager::GetGlobalInstance().Emit("SocReviveEvent_AirDropGetChestList",
		SandboxContext(nullptr)
		.SetData_Number("ret", protoHC.ret())
		.SetData_String("msg", protoHC.msg())
		.SetData_String("list", jsonstr)
	);
}


void MpGameSurviveNetHandler::handleDelAirDropChest2Client(const PB_PACKDATA_CLIENT& pkg) {
	PlayerControl* playerCtrl = m_root->getPlayerControl();
	if (playerCtrl == NULL || playerCtrl->getWorld() == NULL) return;
	PB_AIRDROP_DelChest protoHC;
	protoHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	jsonxx::Array chestArray;
	for (int i = 0; i < protoHC.list_size(); i++) {
		ChestItem item = protoHC.list(i);
		jsonxx::Object chestObj;
		chestObj << "eventId" << item.eventid();
		chestObj << "spawnTime" << item.spawntime();
		chestObj << "pos_x" << item.pos().x();
		chestObj << "pos_y" << item.pos().y();
		chestObj << "pos_z" << item.pos().z();
		chestArray << chestObj;
 	}

	std::string jsonstr = chestArray.json();

	SandboxEventDispatcherManager::GetGlobalInstance().Emit("SocReviveEvent_AirDropDelChest",
		SandboxContext(nullptr)
		.SetData_String("list", jsonstr)
	);
}


void MpGameSurviveNetHandler::handleResearch2Client(const PB_PACKDATA_CLIENT& pkg)
{

}

void MpGameSurviveNetHandler::handleTechBlueprint2Client(const PB_PACKDATA_CLIENT& pkg)
{
	PlayerControl* playerCtrl = m_root->getPlayerControl();
	if (playerCtrl == NULL || playerCtrl->getWorld() == NULL) return;
	PB_TechBlueprintHC protoHC;
	protoHC.ParseFromArray(pkg.MsgData, pkg.ByteSize);

	SandboxEventDispatcherManager::GetGlobalInstance().Emit("TechTree_blueprintlock",
		SandboxContext(nullptr)
		.SetData_Number("code", 0)
		.SetData_Number("level", protoHC.level())
		.SetData_Number("nodeid", protoHC.nodeid())
	);
}