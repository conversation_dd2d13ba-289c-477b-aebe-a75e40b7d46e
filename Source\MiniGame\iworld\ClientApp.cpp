﻿#include "ClientApp.h"
#include "MiniReportMgr.h"
#include "IWorldConfig.h"
#include "ClientApp.h"
#include "GetClientInfo.h"
#include "ClientLogin.h"
#include "OgreScriptLuaVM.h"
#include "GameRuntime.h"
#include "SandboxRuntime.h"
#include "SandboxCore.h"
#include "PlatformUtility.h"
#include "CommonUtil.h"
#include "MacroControlStatistics.h"
#include "LuaInterface.h"
#include "GameLoader.h"
#include "PluginManager.h"
#include "SandBoxManager.h"
#include "WorldArchiveMgr.h"
#include "WorldBackupMgr.h"
#include "RoomManager.h"
#include "ArchiveManager.h"
#include "OnlineService.h"
#include "TriggerLangEditor/EditorLang.h"
#include "Advertisement.h"
#include "PlatformSdkManager.h"
#include "SnapshotMgr.h"
#include "SnapshotForShare.h"
#include "SnapshotForGame.h"
#include "ClientBuddyMgr.h"
#include "UIActorBodyMgr.h"
#include "ClientAccount.h"
#include "Achievementmanager.h"
#include "DebugDataMgr.h"
#include "PermitsManager.h"
#include "Socket/DebugSocketMgr.h"

#ifdef MODULE_FUNCTION_ENABLE_AR
#include "ARInterface.h"
#endif

#include "ModManager.h"
#include "ModEditorManager.h"
#include "OgreModFileManager.h"
#include "UIProjectLibManager.h"
#include "UIEditorManager.h"
#include "UIFrameManager.h"
#include "BluePrintMgr.h"
#include "RoomSyncResMgr.h"
#include "MapEditManager.h"
#include "AIFunctionMgr.h"
#include "WorldStringManager.h"
#include "ScriptSupportMgr.h"
#include "WorldManager.h"

#include "GameSettings.h"
#include "UIStageSandboxObject.h"
#include "CameraManager.h"
#include "StarStationTransferMgr.h"

#include "OgrePhysXManager.h"
#include "TriggerScriptMgr.h"
#include "defmanager.h"
#include "ClientGameManager.h"

#include "GameStatic.h"
#include "base/CCDirector.h"
#include "GameUI.h"
#include "StringDefCsv.h"
#include "GlobalFunctions.h"
#include "AccountFBData.h"

#include "event/MiniUIGameEventMgr.h"
#include "Misc/FrameTimeManager.h"
#include "Graphics/ScreenManager.h"

#include "ChannelConfig.h"
#include "IRecordInterface.h"
#include "MacroControlSpringFestival.h"

#include "Network/LuaWebSocketMgr.h"
#include "PlayerControl.h"
#include "Download/LegacyDownloadManager.h"
#include "Utilities/Logs/LogSystem.h"
#include "GfxDevice/GfxDeviceSetting.h"

#include "genCustomModel.h"
#include "ClientInfo.h"
#include "OWorldList/OWorldList.h"
#include "SandboxGame/Resource/Encrypt/DevEncrypt.h"
#include "Resource/3DUI/CustomPic/CustomPicMgr.h"
#include "SoundPlayer/Midi/MidiFileInfo.h"
#include "Platforms/PlatformInterface.h"
#include "gamePlatSDKUtils/DeviceIDHelper.h"

#include "Bootstrap/BootConfig.h"
#include "MultiLocalMgr.h"
#include "ClientManager.h"
#include "Dev3DUIManager.h"
#include "SprayPaintMgr.h"
#include "MacroControlStatistics.h"
#include "ArchiveTempMgr.h"
#include "OgreStringUtil.h"
#include "world_types.h"
#include "ICef3Manager.h"

#include "Core/display/MinimapRenderer.h"
#include "gameFunction/MiniWorldPreferences.h"
#include "Misc/PlayerSettings.h"
#include "Misc/GameSetting.h"
#include "File/Packages/PackageAsset.h"
#include "File/CacheManager.h"
#include "Bootstrap/BootConfig.h"

#include "Platforms/PlatformInterface.h"
#include "Core/display/SandboxRenderSetting.h"
#include "MiniHotfix/HotfixPkgLoader.h"
#ifdef MODULE_FUNCTION_ENABLE_ACESDK
#include "acesdk_impl.h"
#endif

#if PLATFORM_IOS
#include "Platforms/IPhone/EngineInterface.h"
#endif

#if PLATFORM_WIN
#include "MicroUpdateMgr.h"
#include <shlobj.h>
#include <ShellAPI.h>
#include "WinUtil.h"
#include "Platforms/Win/WinUnicode.h"
#include "Platforms/Win/PathUnicodeConversion.h"
#include "platform/win32/CrashHandler.h"
#include "platform/win32/MiniDumper.h"
#include "WindowsCallback.h"
#include "GameGMMgr.h"
#elif PLATFORM_ANDROID
#include "gameFunction/ReachabilityManager.h"
#include "Platforms/Android/GameBaseActivity.h"
#include "Platforms/Android/AppPlayJNI.h"
#include "Platforms/Android/MNWUser.h"
#elif PLATFORM_OHOS
#include "Platforms/OHOS/napi/file/FileUtils.h"
#elif PLATFORM_IOS
#include "Platforms/IPhone/EngineInterface.h"
#endif

#ifdef DEDICATED_SERVER
#include "ZmqProxy.h"
#include "ClientGameStandaloneServer.h"
#include "SSDataMgr.h"
#include "TeleportRegister.h"
#endif
#include "UIRenderer.h"
#include "Profiler/ProfilerSample.h"
#include "Tools/MapParse/MapParse.h"
#include "Console/Console.h"

#ifdef IWORLD_UNIVERSE_BUILD
#include "MiniUniverse/MiniReport/MiniReportMgr.h"
#include "BaseHotfixPkgLoader.h"
#include "ModuleRes/ModuleResManager.h"
#include "MiniMutualParams.h"
#else
#include "MiniDomestic/MiniReport/MiniReportMgr.h"
#endif

#include "ActionLogger.h"

#include "VMProtect.h"
#include "sandboxConfig/SandboxCfg.h"
#include "SandboxGlobalNotify.h"
#include "UIScene.h"
#include "Common/StatisticHelper.h"
#include "Platforms/PlatformInterface.h"
#include "GameInfo.h"
#include "StatisticsManager.h"
#include "HomeCsvManager.h"
#include "IWorldExports.h"
#include "SandboxRenderSetting.h"
#include "Input/InputManager.h"
#include "File/Packages/FileCompressHelper.h"
#include "WXGameLiveManager.h"
#include "Utilities/GUID.h"
#include "Modules/LLM/LLMRegisterLua.h"

#ifdef MODULE_FUNCTION_ENABLE_ACESDK
#include "acesdk_impl.h"
#endif

#ifdef MODULE_FUNCTION_ENABLE_GAMERECORD
#include "RecordPkgManager.h"
#endif

#ifdef MODULE_FUNCTION_ENABLE_SNAPSHOT
#include "SnapshotMgr.h"
#endif

#ifdef MODULE_FUNCTION_ENABLE_STATISTICS
#include "GameStatistics/StatisticsTools.h"
#include "GameStatistics/TerrgenStatistic.h"
#endif

#ifdef BUILD_MINI_EDITOR_APP
#include "EditorSceneSettingMgr.h"
#endif

//#include "Core/nodes/chatBubble/SandboxChatBubbleManager.h"

#ifdef IS_POCOSDK_ENABLE
#include "PocoSDK/Public/PocoStartServer.h"
#include "PocoSDK/Public/PocoScreenShotMgr.h"
#include "PocoSDK/Public/PocoServer.h"
#include "Debug/DebugMgr.h"
#endif

#include "GameCloudProxy.h"
#include "Optick/optick.h"
#include "IModuleConf.h"
#include "Libfairygui/GLoader.h"

#include "Core/GameScene.h"

#include "CustomModelWaitSyncListMgr.h"

#include "SandboxActorSubsystem.h"

#include "Configuration/GameConfigMgr.h"
#include "SandboxNodeRegisterEx.h"
#include "UIRenderer.h"
#include "TextRendering/FontAtlas.h"
#include "TextRendering/FontAtlasCache.h"
#include "ClientDnsMgr.h"
#include "DHKeyMgr.h"
#include "Base64Encoder.h"

using namespace Rainbow;
using namespace Rainbow::UILib;

TOLUA_API void toluafix_open(lua_State* L);
USING_NS_FGUI;


//用于允许vs中调试release版本的程序，1表示不允许，0表示允许
#define DISABLE_DEBUGGER_RUNNING 1


STATIST_HELPER_DECLEAR(EXPORT_IWORLD, ActorBody)
extern void DEVUISetIconByResIdEx(fairygui::GLoader* pLoader, std::string nLongId);

namespace Rainbow {
	STATIST_HELPER_DECLEAR(EXPORT_LEGACYMODULE, Model)
	STATIST_HELPER_DECLEAR(EXPORT_LEGACYMODULE, Entity)
	STATIST_HELPER_DECLEAR(EXPORT_LEGACYMODULE, ParametricShapeRenderer)
	STATIST_HELPER_DECLEAR(EXPORT_LEGACYMODULE, LegacyParticleEmitterRenderer)
	STATIST_HELPER_DECLEAR(EXPORT_LEGACYMODULE, LegacyRibbonEmitterRenderer)
	STATIST_HELPER_DECLEAR(EXPORT_LEGACYMODULE, LegacyBeamEmitterRenderer)
	STATIST_HELPER_DECLEAR(EXPORT_LEGACYMODULE, LegacyBillboardRenderer)
}

namespace MINIW
{

	static void UIRenderCallback()
	{
		ClientGame* pcurgame = GetClientGameManagerPtr()->getCurGame();
		if (pcurgame) pcurgame->renderUI(GetGameUIPtr()->isUIHide());

		UIScene* scene = dynamic_cast<UIScene*>(cocos2d::Director::getInstance()->getRunningScene());

		GetGameUIPtr()->Render();

		//MNSandbox::SandboxChatBubbleManager::GetInstancePtr()->renderUI(); 改成通过监听GlobalNotify::m_RenderUI 回调

#if MINI_NEW_UI
		//cocos2d::Director::getInstance()->render();
#endif

		//DebugDataMgr的renderui已经不用旧ui的实现方式了，这里不需要判断 if (!GetGameUIPtr()->isUIHide())
		{
#ifdef BUILD_MINI_EDITOR_APP
			EditorSceneSettingMgr::GetInstancePtr()->renderUI();
#else
			DebugDataMgr::GetInstancePtr()->renderUI();
#endif
		}

		if (pcurgame) pcurgame->renderUIEffect();
	}

	static const char androidLogPriorityMap[kLogTypeNumLevels] =
	{
		'E',  // kLogTypeError     -> ANDROID_LOG_ERROR
		'A',  // kLogTypeAssert    -> ANDROID_LOG_FATAL
		'W',  // kLogTypeWarning   -> ANDROID_LOG_WARN
		'I',  // kLogTypeLog       -> ANDROID_LOG_INFO
		'F',  // kLogTypeException -> ANDROID_LOG_FATAL
		'D',  // kLogTypeDebug     -> ANDROID_LOG_DEBUG
	};

	static void crashLog(int logType, const char* tag, const char* text)
	{
#if PLATFORM_ANDROID && defined(IWORLD_UNIVERSE_BUILD) // 只有海外安卓版才需要 firebase-crashlytics
		assert(0 <= logType && logType < kLogTypeNumLevels);
		if (logType < 0 || logType >= kLogTypeNumLevels)
			return;

		char buffer[4096];
		snprintf(buffer, sizeof(buffer), "%c/%s: %s", androidLogPriorityMap[logType], tag, text);
		MINIW::crashlyticsLog(buffer);
#endif
	}

	ClientApp::ClientApp()
	{
		GlobalCallbacks::Get().logSystemCallback.Register(MINIW::crashLog);
#if PLATFORM_WIN
		GlobalCallbacks::Get().WndProcCallbacks.Register(MINIW::client_custom_wndproc);
#endif
	}

	ClientApp::~ClientApp()
	{
		GlobalCallbacks::Get().logSystemCallback.Unregister(MINIW::crashLog);
#if PLATFORM_WIN
		GlobalCallbacks::Get().WndProcCallbacks.Unregister(MINIW::client_custom_wndproc);
#endif
	}

	void ClientApp::OnCmd(const char* cmd, Rainbow::ConsoleOutputDevice* output)
	{
		core::string cmdline = cmd;
		cmdline = ToLower(cmdline);
		std::vector<core::string> params;
		Split(cmdline, " ", params);

		if (params.size() < 1)return;

		if (params[0] == "free")
		{
			if (params.size() < 2) return;
			static CameraControlType gs_type = CameraManager::GetInstancePtr()->m_ControlType;
			if (params[1] == "1")
			{
				gs_type = CameraManager::GetInstancePtr()->m_ControlType;
				CameraManager::GetInstancePtr()->switchCameraControleType(FreeFlyControl);
			}
			else
			{
				CameraManager::GetInstancePtr()->switchCameraControleType(gs_type);
			}
			return;
		}

		if (params[0] == "game")
		{
			if (params.size() < 2) return;
			if (params[1] == "stat")
			{
#ifndef DEDICATED_SERVER
#if (DEBUG_MODE || PROFILE_MODE)
				output->Log(Format("actorbody:%d,entity:%d,ParametricShape:%d,ParticleEmitter:%d,RibbonEmitter:%d,BeamEmitter:%d,Billboard:%d",
					STATIST_HELPER_GET_COUNTER(ActorBody), STATIST_HELPER_GET_COUNTER(Entity)
					, STATIST_HELPER_GET_COUNTER(ParametricShapeRenderer)
					, STATIST_HELPER_GET_COUNTER(LegacyParticleEmitterRenderer)
					, STATIST_HELPER_GET_COUNTER(LegacyRibbonEmitterRenderer)
					, STATIST_HELPER_GET_COUNTER(LegacyBeamEmitterRenderer)
					, STATIST_HELPER_GET_COUNTER(LegacyBillboardRenderer)
				).c_str());
#endif
#endif				
				return;
			}
		}

		ClientGame* pcurgame = ClientGameManager::getInstance()->getCurGame();
		if (pcurgame)
		{
			PlayerControl* playerControl = pcurgame->getPlayerControl();
			if (playerControl)
			{
				playerControl->execCmd(cmd);
			}
		}
	}

// 远程链接时开启此选项，才能保证鼠标正常设置位置
#define DEBUG_CURSOR_ALWAYS_SHOW 0

	void ClientApp::AppInit()
	{
#if PLATFORM_WIN && !BUILD_MINI_EDITOR_APP && !defined(DEDICATED_SERVER)
		//WIN_UTIL::InitApplicationCrashHandler();
		WIN_UTIL::InitPCCrashHander();
#endif

		//本地云服接入Sentry
#ifdef STUDIO_SERVER
		WIN_UTIL::InitPCCrashHander();
#endif

		StringUtil::init();

		//m_CreateCustomRenderSettingCallback = CreateSandboxRenderSetting;
		double t0 = GetTimeSinceStartup();
		GameApp::AppInit();
		double t1 = GetTimeSinceStartup();

		WarningStringMsg("GameApp::AppInit() costs %fs , engine version:%s", (t1 - t0), GetClientInfo()->GetEngineVersionStr().c_str());

#if ENABLE_MEMORY_MANAGER
		WarningString("ENABLE_MEMORY_MANAGER == 1");
#else
		WarningString("ENABLE_MEMORY_MANAGER == 0");
#endif
		Rectf rect = GetScreenManagerPtr()->GetScaleScreen();
		WarningStringMsg("Screen,width:%f,heihgt:%f",rect.width,rect.height);

#if DEBUG_CURSOR_ALWAYS_SHOW
		GetScreenManager().SetAllowCursorHide(false);
#endif

#ifdef MODULE_FUNCTION_ENABLE_ACESDK
		AceSdkMgr::OnInit();
#endif

		InitGameAnalytics();

        CONSOLE_REGIST_CMD_HANDLER(ClientApp, OnCmd, this);
#if USE_METRIC_STATICS
        METRIC_INFO(app_info, { "EngineVer",    GetClientInfo()->GetEngineVersionStr().c_str()});
        METRIC_INFO(app_info, { "AppVer",       GetClientInfo()->GetClientVersionStr().c_str()});
        METRIC_INFO(app_info, { "CompileVer",   GetClientInfo()->GetCompileVersionStr().c_str()});
        //每次启动加一个唯一ID
        time_t st = time(NULL);
        UInt32 uid = GenerateGUID().PackToUInt32();
        dynamic_array<UInt8> data;
        data.resize_uninitialized(sizeof(st) + sizeof(uid));
        memcpy(data.data(), &st, sizeof(st));
        memcpy(data.data() + sizeof(st), &uid, sizeof(uid));
        char szBase64[64] = { 0 };
        //这个encoding末尾加了\r\n，没有用，去掉
        int len = Base64Encoding(data.data(), (int)data.size(), szBase64);
        if (len >= 2) {
            szBase64[len - 2] = 0;
            szBase64[len - 1] = 0;
        }
        LogStringMsg("Metric SessionID:%s, st=%llu, uid=%u", szBase64, st, uid);
        METRIC_INFO(app_info, { "SessionID",   szBase64 });
        METRIC_GAUGE_SET(app_game_state, 1, { "type", "Running" });
#endif // USE_METRIC_STATICS
	}

    void CleanExpiredCacheFiles()
    {
//        const char* kCacheModuleName = "CloudAssets";
//        const char* kCloudCachePath  = "asset_cache/CloudAssets";
//#if PLATFORM_WIN
//        const int kCacheLimitMaxSizeMB = 4096; // Windows 缓存上限设为 4G
//#else
//        const int kCacheLimitMaxSizeMB = 512;
//#endif

        /*bool ret = Rainbow::GetCacheManager().Init(kCacheModuleName, kCloudCachePath, false);
        if (ret) {
            Rainbow::GetCacheManager().CleanExpiredCacheFiles(kCacheModuleName, kCacheLimitMaxSizeMB);
        }
        else {
            ErrorStringMsg(">> GetCacheManager init(name '%s', path '%s') failed", kCacheModuleName, kCloudCachePath);
        }*/
    }

	void ClientApp::BeginPlay()
	{
		VMP_BEGIN("ClientApp_BeginPlay");
		m_LastGameTickAccum = 0.0f;
		m_GameTickAccum = 0.0f;
		m_NetTickAccum = 0.0f;
#ifdef SANDBOX_HIGH_TICK
		MNSandbox::Core::ResetTickAccum();
#endif
		//m_frameCount = 0;
		this->SetFrameTickTime(80);
		this->SetSlowTickFrame(20);

#if defined(WIN32) && !defined(DEDICATED_SERVER)
		//设置窗口title
#ifdef IWORLD_UNIVERSE_BUILD
		std::wstring wideName = L"代号:OMO";
#else
		std::wstring wideName = L"代号:OMO";
#endif
		HWND _hwnd = Rainbow::GetScreenManager().GetWindow();
		SetWindowTextW(_hwnd, wideName.c_str());
#endif

		GameRuntimeInitializeAndCleanup::ExecuteInitializations();

		//ClientAPPSuper1::BeginPlay();
		Rainbow::GameApp::BeginPlay();

#ifdef MODULE_FUNCTION_ENABLE_STATISTICS
		StatisticsTools::getInstance();
#endif
		
#ifndef DEDICATED_SERVER
		if (!checkVMPValid())
		{
			VMP_END;
			return;
		}

#ifdef IS_POCOSDK_ENABLE
		if (GetDebugMgr().UsePocoTest())
		{
			PocoTest::PocoServerStart();
		}
#endif

#if DISABLE_DEBUGGER_RUNNING
		if (checkDebuggerRunning())
		{
			VMP_END;
			return;
		}
#endif

#ifndef BUILD_MINI_EDITOR_APP
#ifdef WIN32
		if (checkDoubleOpenings())
		{
			VMP_END;
			return;
		}

		if (preOpenMicroMini())
		{
			VMP_END;
			return;
		}
#endif
		if (checkUnderVM())
		{
			VMP_END;
			return;
		}
#endif// BUILD_MINI_EDITOR_APP
#endif

		MNSandbox::NodeRegisterEx::InitNodes();
		InitSandboxRenderSettings();

		GetDefManager(); //提前实例化，避免使用时序不对导致的崩溃
		GetGameCloudProxy()->Init();

		CreateClientLogin();

#ifdef MODULE_FUNCTION_ENABLE_STATISTICS
        StatisticsTools::getInstance();
#endif

#if PLATFORM_WIN
		// 游戏界面出现以后，再发通知
		HANDLE hEvent = OpenEvent(EVENT_ALL_ACCESS, FALSE, _T("{D591791F-2756-40C6-AAE1-5C5003201223}"));
		if (hEvent != NULL)
		{
			SetEvent(hEvent);
			CloseHandle(hEvent);
		}
#endif

#ifndef DEDICATED_SERVER 
		GetGameUIPtr()->InitNewMiniUI();
		UIRenderer::GetInstance().setPreRenderCallback(UIRenderCallback);
#endif

		GetClientInfo()->InitThirdWindows();//初始化第三方窗口 例如微端之类的
		GameSetting& gameSetting = GetGameSetting();

		//初始化字体，在热更新初始化之前执行
		InitFont();

		//先执行 httpdns 解析，然后再做上报和热更 code_by:huangfubin 2024.6.25
		StartDnsConvert();

//#ifdef IWORLD_UNIVERSE_BUILD
//		//原逻辑移动到函数中了
//		DisptchHotfix();
//#else
//		GetClientInfo()->InitThirdWindows();//初始化第三方窗口 例如微端之类的
//		GameSetting& gameSetting = GetGameSetting();
//#if PLATFORM_WIN || PLATFORM_APPLE
//		STATISTICS_INTERFACE_EXEC(gameOpenSplash(),0);
//#endif
//		if (RAINBOW_EDITOR || !IsAssetDevelopMode()) {
//			//如果有内置的pkg是压缩的
//			if (checkInsidePkgCompress())
//					{
//				int network = GetClientInfo()->getNetworkState();
//				if (gameSetting.m_UseHotfix && (network != 0))
//				{
//					WarningStringMsg("Use HotFix>");
//					m_ClientLogin->StartHotfix(true);
//				}
//				else
//				{
//					m_ClientLogin->StartHotfix(false);
//				}
//			}
//			else
//			{
//				if (gameSetting.m_UseHotfix)
//				{
//					int network = GetClientInfo()->getNetworkState();
//					WarningStringMsg("Use HotFix>");
//					//没有网络连接
////					if (network == 0)
////					{
////						WarningStringMsg("network == 0");
////						//判断本地是否有完整的全量包
////						bool bCheckPkg = true;
////						dynamic_array<GamePackageInfo>& infoList = gameSetting.m_PkgLists;
////						for (int i = 0; i < infoList.size(); i++) {
////							GamePackageInfo& info = infoList[i];
////							bool isSuportRenderer = true;
////							if (info.renderer != kGfxRendererUnknown && info.renderer != Rainbow::GetGfxDeviceSetting().GetGfxDeviceRenderer())
////							{
////								isSuportRenderer = false;
////							}
////							if (isSuportRenderer) {
////								core::string fullPkgFile = "";
////								core::string useageFullFile = ToPkgUseageFullFile(info.pkgFilePath.c_str());
////								if (GetGameSetting().m_AndroidPKGMode == 2) // InsideApk pkg 模式
////								{
////#if PLATFORM_IOS
////									fullPkgFile = Format("%s/%s", EngineApplicationDir(), info.pkgFilePath.c_str());
////#else
////									fullPkgFile = "assets/" + info.pkgFilePath;
////#endif
////									int baseVersion = PackageAsset::GetPkgFileVersion(fullPkgFile);
////									int newVersion = PackageAsset::GetPkgFileVersion(useageFullFile);
////									if (baseVersion <= newVersion)
////									{
////										fullPkgFile = useageFullFile;
////									}
////									WarningStringMsg("pkg name:%s,fullPkgFile:%s %d %d", info.pkgFilePath.c_str(), fullPkgFile.c_str(), baseVersion, newVersion);
////								}
////								else
////								{
////									fullPkgFile = useageFullFile;
////								}
////								FilePkgBase* basePkg = GetFileManager().FindPackage(info.name.c_str());
////								if (basePkg != NULL)
////								{
////									GetFileManager().RemovePackage(basePkg->GetName().c_str());
////								}
////
////								FilePkgBase* pkgBase = GetFileManager().AddPackage(Rainbow::FILEPKG_ASSET, info.name.c_str(), fullPkgFile.c_str(), info.priority, FILETYPE_SERIALIZE_FILE);
////								assert(pkgBase);
////								if (pkgBase == nullptr)
////								{
////									bCheckPkg = false;
////									break;
////								}
////								else
////								{
////									pkgBase->SetFilePrefix(info.filePrefix);
////									PackageAsset* packageAsset = static_cast<PackageAsset*>(pkgBase);
////									//把最新的版本号设置进去
////									GetGameSetting().SetPkgVersion(i + 2, packageAsset->GetResVersion());
////									LogStringMsg("pkg:%s,version:%d", info.pkgFilePath.c_str(), packageAsset->GetResVersion());
////								}
////							}
////							else {
////								GetGameSetting().SetPkgVersion(i + 2, 0);
////								LogStringMsg("not suport pkg:%s,version:%d", info.pkgFilePath.c_str(), 0);
////							}
////						}
////						//有PKG不存在， 需要退出游戏
////						if (!bCheckPkg)
////						{
////							m_ClientLogin->StartHotfix();
////						}
////						else   // 有PKG存在，可以进单机游戏
////						{
////							m_ClientLogin->PostHotfix();
////						}
////
////				}
////					else
////					{
//						WarningStringMsg("m_ClientLogin->StartHotfix()");
//						m_ClientLogin->StartHotfix();
////					}
//
//				}
//				else {
//					//表示读取本地pkg包的模式
//					WarningStringMsg("Use Local Package>");
//						dynamic_array<GamePackageInfo>& infoList = gameSetting.m_PkgLists;
//						for (int i = 0; i < infoList.size(); i++) {
//							GamePackageInfo& info = infoList[i];
//							bool isSuportRenderer = true;
//							if (info.renderer != kGfxRendererUnknown && info.renderer != Rainbow::GetGfxDeviceSetting().GetGfxDeviceRenderer())
//							{
//								isSuportRenderer = false;
//							}
//							if (isSuportRenderer) {
//#if PLATFORM_IOS
//							// todo iOS read from setting, move to GameAppEntry, remove this macro
//							core::string asset = Format("%s/%s", EngineApplicationDir(), info.pkgFilePath.c_str());
//
//							FilePkgBase* pkgBase = GetFileManager().AddPackage(Rainbow::FILEPKG_ASSET, info.name.c_str(), asset.c_str(), info.priority, FILETYPE_SERIALIZE_FILE);
//
//#elif PLATFORM_ANDROID
//							core::string insidePkgPath = "assets/" + info.pkgFilePath;
//							core::string asset;
//							if (info.pkgFilePath.starts_with('/'))  // sdcard 绝对路径
//								asset = info.pkgFilePath;
//							else if (GetGameSetting().m_AndroidPKGMode == 2 && (!FileCompressHelper::CheckLZMAFileCorrect(insidePkgPath.c_str())))
//								asset = insidePkgPath;
//								else
//								asset = ToPkgUseageFullFile(info.pkgFilePath.c_str());
//
//							FilePkgBase* pkgBase = GetFileManager().AddPackage(Rainbow::FILEPKG_ASSET, info.name.c_str(), asset.c_str(), info.priority, FILETYPE_SERIALIZE_FILE);
//#else 
//							FilePkgBase* pkgBase = nullptr;
//
//							FileAccessor pkgAccessor;
//							if (pkgAccessor.Open(info.pkgFilePath.c_str(), FilePermission::kReadPermission)) {
//								pkgBase = GetFileManager().AddPackage(Rainbow::FILEPKG_ASSET, info.name.c_str(), info.pkgFilePath.c_str(), info.priority, FILETYPE_SERIALIZE_FILE);
//								pkgAccessor.Close();
//								}
//#endif					
//							if (pkgBase)
//								{
//									pkgBase->SetFilePrefix(info.filePrefix);
//									PackageAsset* packageAsset = static_cast<PackageAsset*>(pkgBase);
//									//把最新的版本号设置进去
//									GetGameSetting().SetPkgVersion(i + 1, packageAsset->GetResVersion());
//								WarningStringMsg("pkg:%s,version:%d", info.pkgFilePath.c_str(), packageAsset->GetResVersion());
//								}
//							}
//							else {
//								GetGameSetting().SetPkgVersion(i + 1, 0);
//							WarningStringMsg("not suport pkg:%s,version:%d", info.pkgFilePath.c_str(), 0);
//							}
//						}
//					//没有热更新
//					m_ClientLogin->PostHotfix(true);
//						}
//						}
//				}
//		else {
//#if PLATFORM_ANDROID || PLATFORM_IOS
//			LogStringMsg("DevelopMode mode not support in mobile!!!");
//#endif
//			WarningStringMsg("not hotfix  m_ClientLogin->PostHotfix()");
//			//没有热更新
//			m_ClientLogin->PostHotfix(true);
//					}
//
//#if PLATFORM_ANDROID
//		WarningStringMsg("PostHotfix removeSplashView start");
//		GameBaseActivity::removeSplashView();
//		WarningStringMsg("PostHotfix removeSplashView end");
//#endif
//#endif //IWORLD_UNIVERSE_BUILD
        removeSplashView();
		VMP_END;
	}

	void ClientApp::AppExit()
	{
		WarningString("ClientApp::AppExit()");

		if(GetInputManagerPtr())
			GetInputManagerPtr()->QuitApplication();

		CONSOLE_UNREGIST_CMD_HANDLER(ClientApp, OnCmd, this);

		//游戏配置管理器实例
		if (Rainbow::Setting::GameConfigMgr::Inst())
		{
			Rainbow::Setting::GameConfigMgr::DestoryInstance();
		}

		//热更新的进度管理
		DestoryHotfixProcessMgr();

		DevEncrypt::Destroy();
		
		PlatformSdkManager::Destroy();

		UIProjectLibManager::Destroy();

		UIEditorManager::Destroy();

		/*auto uiframeMgr = UIFrameManager::getSingleton();
		ENG_DELETE(uiframeMgr);

		auto cuspicMgr = CustomPicMgr::getSingleton();
		ENG_DELETE(cuspicMgr);
		*///得在GameUI释放之后释放


		SafelyDestroyGameUI();

		if (GET_SUB_SYSTEM(UIActorBodyMgr))
		{
			GET_SUB_SYSTEM(UIActorBodyMgr)->Destory();
		}

		MidiFileInfo::Destroy();

		RoomSyncResMgr::destroySingleton();

		MultiLocalMgr::Destroy();

		//call lua exit
		//GetCoreLuaDirector().CallFunction("G_On_App_Exit");
		GetClientGameManagerPtr()->releaseGameData();

		GetClientInfo()->destroy();
		ENG_DELETE(m_ClientLogin);
		
	

	

		//must after CTriggerObjLibManager::~CTriggerObjLibManager
		SprayPaintMgr::Destroy();

		Dev3DUIManager::Destroy();


		//统计
#ifdef MODULE_FUNCTION_ENABLE_STATISTICS
		StatisticTerrgen::Destroy();
#else
		
#endif // MODULE_FUNCTION_ENABLE_STATISTICS
		ArchiveTempMgr::Destroy();

		GetSandboxActorSubsystemIns().Shut();
		//lua虚拟机在此释放
		GameRuntimeInitializeAndCleanup::ExecuteCleanup();

		MNSandbox::Core::Destroy();

#if PLATFORM_WIN && !BUILD_MINI_EDITOR_APP && !defined(DEDICATED_SERVER)
		WIN_UTIL::ClosePCCrashHander();
#endif

		GameApp::AppExit();
		MINIW::SafelyDestroyPhysXManager();
	}

	void ClientApp::InitFont()
	{
#ifndef IWORLD_SERVER_BUILD
		fairygui::UIConfig::registerFont(fairygui::UIConfig::defaultFont, Rainbow::GetRainbowDefaultFont());
		{
			MultiLocalMgr::getSingleton()->InitFontsCfg();
			//本地化字体初始化
			if (!MultiLocalMgr::getSingleton()->FontsCfgIsEmpty())
			{
				std::vector<std::string>& fontsPath = MultiLocalMgr::getSingleton()->GetFontsPath();
				//两套UI 所以需要设置两次
				Rainbow::UILib::UIRenderer::GetInstance().SetMultiLocalFontsPath(fontsPath);
				////新UI这次的设置会更新引擎内部字体缓存
				//cocos2d::Director::getInstance()->setMultiLocalFontsPath(fontsPath);
				////存在多语言,需要将海外优秀级最高的字体设置为默认字体,防止因为引擎默认字体缺失,出现底层GUI文字失效的问题
				//Rainbow::SetRainbowDefaultFont(fontsPath[0].c_str());
			}
		}
#endif
	}

	void ClientApp::DisptchHotfix()
	{		
		//清理过期历史资源包: 任何更新前处理
		ClientLogin::ClearHistoryUpdate();
		ExcuteUniverseBasePkgHotfix();
	}

	void ClientApp::StartDnsConvert()
	{
#ifndef DEDICATED_SERVER 		
		//获取网路
		int network = GetClientInfo()->getNetworkState();
		//有网络
		if (network != 0)
		{
			m_ClientLogin->StartDnsConvert();
		}
		else
#endif			
		{
			AfterDnsConvert();
		}
	}

	void ClientApp::AfterDnsConvert()
	{
		//上报游戏数据
		STATISTICS_INTERFACE_EXEC(launchGame(1), 0);
		//开始游戏
		m_ClientLogin->LoginStep1();

#if PLATFORM_WIN || PLATFORM_APPLE
		STATISTICS_INTERFACE_EXEC(appStartup(), 0);
#endif

#if PLATFORM_WIN || PLATFORM_APPLE
		STATISTICS_INTERFACE_EXEC(gameOpenSplash(), 0);
#endif

		//原逻辑移动到函数中了
		DisptchHotfix();

		CleanExpiredCacheFiles();
	}


    void ClientApp::ExcuteBasePkgHotfix()
    {
        bool isEnableColdUpdate = enableNewAppUpdate();
        WarningStringMsg("[AppUpdate]isEnableColdUpdate=%s", isEnableColdUpdate ? "true" : "false");
        GameSetting &gameSetting = GetGameSetting();
        if (RAINBOW_EDITOR || !IsAssetDevelopMode())
        {
            if (isEnableColdUpdate || gameSetting.m_UseHotfix)
            {
                int network = GetClientInfo()->getNetworkState(true);
                // 没有网络连接
                if (network == 0)
                {
                    /*WarningStringMsg("[AppUpdate]network==0 isEnableColdUpdate=%s", isEnableColdUpdate ? "true" : "false");
                    m_ClientLogin->StartLaunch(isEnableColdUpdate);*/
                    // 判断本地是否有完整的全量包
                    bool bCheckPkg = true;
                    dynamic_array<GamePackageInfo> &infoList = gameSetting.m_PkgLists;
                    for (int i = 0; i < infoList.size(); i++)
                    {
                        GamePackageInfo &info = infoList[i];
                        bool isSuportRenderer = true;
						bool isLuajitMeet = true;
                        if (info.renderer != kGfxRendererUnknown && info.renderer != Rainbow::GetGfxDeviceSetting().GetGfxDeviceRenderer())
                        {
                            isSuportRenderer = false;
                        }
#if PLATFORM_ARCH_32
						int luajitVersion = 32;
#else
						int luajitVersion = 64;
#endif

						if (info.luajitVersion != 0 && info.luajitVersion != luajitVersion)
						{
							isLuajitMeet = false;
						}

                        if (isSuportRenderer && isLuajitMeet)
                        {
                            core::string fullPkgFile = "";
                            core::string useageFullFile = ToPkgUseageFullFile(info.pkgFilePath.c_str());
                            if (GetGameSetting().m_AndroidPKGMode == 2) // InsideApk pkg 模式
                            {
#if PLATFORM_IOS
                                fullPkgFile = Format("%s/%s", EngineApplicationDir(), info.pkgFilePath.c_str());
#elif PLATFORM_ANDROID
                                fullPkgFile = "assets/" + info.pkgFilePath;
#elif PLATFORM_OHOS
                                fullPkgFile = GetFileManager().IsFileExistWritePath(info.pkgFilePath.c_str())
                                    ? FileUtils::GetWritableFilesPath() + info.pkgFilePath
                                    : FileUtils::GetReadOnlyResourcePath() + info.pkgFilePath;
#endif
                                int baseVersion = PackageAsset::GetPkgFileVersion(fullPkgFile);
                                int newVersion = PackageAsset::GetPkgFileVersion(useageFullFile);
                                if (baseVersion <= newVersion)
                                {
                                    fullPkgFile = useageFullFile;
                                }
                                WarningStringMsg("pkg name:%s,fullPkgFile:%s %d %d", info.pkgFilePath.c_str(), fullPkgFile.c_str(), baseVersion, newVersion);
                            }
                            else
                            {
                                fullPkgFile = useageFullFile;
                            }
							FilePkgBase* basePkg = GetFileManager().FindPackage(info.name.c_str()).Get();
                            if (basePkg != NULL)
                            {
                                GetFileManager().RemovePackage(basePkg->GetName().c_str());
                            }

                            FilePkgBase *pkgBase = GetFileManager().AddPackage(Rainbow::FILEPKG_ASSET, info.name.c_str(), fullPkgFile.c_str(), info.priority, FILETYPE_SERIALIZE_FILE);
                            assert(pkgBase);
                            if (pkgBase == nullptr)
                            {
                                bCheckPkg = false;
                                break;
                            }
                            else
                            {
                                pkgBase->SetFilePrefix(info.filePrefix);
                                PackageAsset *packageAsset = static_cast<PackageAsset *>(pkgBase);
                                // 把最新的版本号设置进去
                                GetGameSetting().SetPkgVersion(i + 1, packageAsset->GetResVersion());
                                LogStringMsg("pkg:%s,version:%d", info.pkgFilePath.c_str(), packageAsset->GetResVersion());
                            }
                        }
                        else
                        {
                            GetGameSetting().SetPkgVersion(i + 1, 0);
                            LogStringMsg("not suport pkg:%s,version:%d", info.pkgFilePath.c_str(), 0);
                        }
                    }

                    // 有PKG不存在， 需要退出游戏
                    if (!bCheckPkg)
                    {
                        WarningStringMsg("[AppUpdate]bCheckPkg==false");
                        if (isEnableColdUpdate)
                        {
                            StartAppUpdate();
                        }
                        else
                        {
                            StartHotfixUpdate();
                        }
                    }
                    else // 有PKG存在，可以进单机游戏
                    {
                        WarningStringMsg("[AppUpdate]bCheckPkg==true");
                        PostHotfix(true);
                    }
                }
                else
                {
                    if (isEnableColdUpdate)
                    {
                        StartAppUpdate();
                    }
                    else
                    {
                        StartHotfixUpdate();
                    }
                }
            }
            else
            {
                // 表示读取本地pkg包的模式

                dynamic_array<GamePackageInfo> &infoList = gameSetting.m_PkgLists;
                for (int i = 0; i < infoList.size(); i++)
                {
                    GamePackageInfo &info = infoList[i];
                    bool isSuportRenderer = true;
					bool isLuajitMeet = true;
                    if (info.renderer != kGfxRendererUnknown && info.renderer != Rainbow::GetGfxDeviceSetting().GetGfxDeviceRenderer())
                    {
                        isSuportRenderer = false;
                    }
#if PLATFORM_ARCH_32
					int luajitVersion = 32;
#else
					int luajitVersion = 64;
#endif

					if (info.luajitVersion != 0 && info.luajitVersion != luajitVersion)
					{
						isLuajitMeet = false;
					}
					
                    if (isSuportRenderer && isLuajitMeet)
                    {
#if PLATFORM_IOS
                        // todo iOS read from setting, move to GameAppEntry, remove this macro
                        core::string asset = Format("%s/%s", EngineApplicationDir(), info.pkgFilePath.c_str());

                        FilePkgBase *pkgBase = GetFileManager().AddPackage(Rainbow::FILEPKG_ASSET, info.name.c_str(), asset.c_str(), info.priority, FILETYPE_SERIALIZE_FILE);

#elif PLATFORM_ANDROID || PLATFORM_OHOS
#   if PLATFORM_ANDROID
                        core::string insidePkgPath = "assets/" + info.pkgFilePath;
#   elif PLATFORM_OHOS
                        core::string insidePkgPath = GetFileManager().IsFileExistWritePath(info.pkgFilePath.c_str())
                            ? FileUtils::GetWritableFilesPath() + info.pkgFilePath
                            : FileUtils::GetReadOnlyResourcePath() + info.pkgFilePath;
#   endif
                        core::string asset;
                        if (info.pkgFilePath.starts_with('/')) // sdcard 绝对路径
                            asset = info.pkgFilePath;
                        else if (GetGameSetting().m_AndroidPKGMode == 2 && (!FileCompressHelper::CheckLZMAFileCorrect(insidePkgPath.c_str())))
                            asset = insidePkgPath;
                        else
                            asset = ToPkgUseageFullFile(info.pkgFilePath.c_str());

                        WarningStringMsg("OHOS>> AddPackage('%s') ...", asset.c_str());
                        FilePkgBase *pkgBase = GetFileManager().AddPackage(Rainbow::FILEPKG_ASSET,
                                                                           info.name.c_str(),
                                                                           asset.c_str(),
                                                                           info.priority,
                                                                           FILETYPE_SERIALIZE_FILE);

#else
                        FilePkgBase *pkgBase = nullptr;

                        FileAccessor pkgAccessor;
                        if (pkgAccessor.Open(info.pkgFilePath.c_str(), FilePermission::kReadPermission))
                        {
                            pkgBase = GetFileManager().AddPackage(Rainbow::FILEPKG_ASSET, info.name.c_str(), info.pkgFilePath.c_str(), info.priority, FILETYPE_SERIALIZE_FILE);
                            pkgAccessor.Close();
                        }
#endif
                        if (pkgBase)
                        {
                            pkgBase->SetFilePrefix(info.filePrefix);
                            PackageAsset *packageAsset = static_cast<PackageAsset *>(pkgBase);
                            // 把最新的版本号设置进去
                            GetGameSetting().SetPkgVersion(i + 1, packageAsset->GetResVersion());
                            LogStringMsg("pkg:%s,version:%d", info.pkgFilePath.c_str(), packageAsset->GetResVersion());
                        }
                    }
                    else
                    {
                        GetGameSetting().SetPkgVersion(i + 1, 0);
                        LogStringMsg("not suport pkg:%s,version:%d", info.pkgFilePath.c_str(), 0);
                    }
                }
                // 没有热更新
                PostHotfix(true);
            }
        }
        else
        {
#if PLATFORM_ANDROID || PLATFORM_OHOS || PLATFORM_IOS
            LogStringMsg("DevelopMode mode not support in mobile!!!");
#endif

            // 没有热更新
            PostHotfix(true);
        }
    }

    void ClientApp::ExcuteUniverseBasePkgHotfix()
    {
		//是否远程更新
		bool isStartUpdate = false;
#if !RAINBOW_EDITOR
		//编辑器||非开发模式
		if (RAINBOW_EDITOR || !IsAssetDevelopMode())
		{
			//获取网路
				int network = GetClientInfo()->getNetworkState();
			//有网络
			if (network != 0)
			{
				//标记启动更新
				isStartUpdate = true;
				//走更新处理(冷更&热更)
				m_ClientLogin->StartUniverseAppUpdate(enableNewAppUpdate(), GetGameSetting().m_UseHotfix);
			}

#if PLATFORM_ANDROID
			LogStringMsg("removeSplashView start");
			GameBaseActivity::removeSplashView();
			LogStringMsg("removeSplashView end");
#endif
		}
		else
		{
#if PLATFORM_ANDROID || PLATFORM_IOS || PLATFORM_OHOS
			LogStringMsg("DevelopMode mode not suport in mobile!!!");
#endif
		}
#endif

		//没有更新，直接启动
		if (!isStartUpdate)
			PostHotfix(true);
	}


	void ClientApp::PostHotfix(bool isImmediately)
	{
		//编辑器||非开发模式
		if (RAINBOW_EDITOR || !IsAssetDevelopMode())
		{
			//加载本地资源包
			if (!m_ClientLogin->LoadNativePkg())
			{
#if !RAINBOW_EDITOR
				return;
#endif
			}
		}

		//开始游戏
		m_ClientLogin->PostHotfix(isImmediately);
	}

    void ClientApp::StartAppUpdate(bool isSkipColdUpdate)
	{
		m_ClientLogin->StartAppUpdate(isSkipColdUpdate);
	}

	void ClientApp::StartHotfixUpdate()
	{
		m_ClientLogin->StartHotfix();
	}



	void ClientApp::InitGameData()
	{
		//m_ClientLogin->InitGameData();
		GetClientInfo()->initGameData();
	}

	void ClientApp::StartUpdate()
	{
		
		MINIW::GameStartUpdate();
	}

	void ClientApp::GameExit(bool restart)
	{
#ifdef IS_POCOSDK_ENABLE
		if (GetDebugMgr().UsePocoTest())
		{
			PocoTest::PocoServerStop();
		}
#endif
        
#if PLATFORM_IOS && IWORLD_UNIVERSE_BUILD
		return;
#endif
        
#if defined(IWORLD_DEV_BUILD ) && defined(IWORLD_LOGIN_CHECK)
		if (GetIWorldConfigPtr()->getGameData("game_env") != 1)
		{
			m_ScriptVM->callFunction("OursValidateFrameGameOut", "");
		}
#endif

		if (GetClientGameManagerPtr()->getCurGame())
		{
			GetClientGameManagerPtr()->getCurGame()->pauseGame();
		}
#if PLATFORM_ANDROID && !IWORLD_UNIVERSE_BUILD
		// 第三方渠道可能有自己的退出窗口，需要延迟确定
		if(!MNWUser::getInstance().isSupport("exit"))
#endif
		GetClientGameManager().clearCurGame();

#if !PLATFORM_LINUX
		MINIW::GameExit(restart);
#endif
#if PLATFORM_WIN
		HWND parenthwnd = (HWND)atol(GetMiniWorldPreferences().getEnterHashValue("parenthwnd").c_str());
		//发送通知退出消息
		::PostMessage((HWND)parenthwnd, WM_CLOSE, 0, 0);
#endif // WIN

	}

	float ClientApp::GetFrameTime()
	{
		return GameApp::GetFrameTickTime();
	}

//	void onHandleGameEvent(GameEvent* ge) 
//	{
//		/*if (PatchManager::getInstance() && PatchManager::getInstance()->isPatching())
//		{
//			PatchManager::getInstance()->onGameEvent(ge);
//		}
//		else*/
//		{
//			//GetGameUIPtr()->SendEvent(GetGameEventQue().getEventName(ge));
//			if (GetClientGameManagerPtr()->getCurGame()) GetClientGameManagerPtr()->getCurGame()->onGameEvent(ge);
//			if (GetClientGameManagerPtr()->getLoadingGame()) GetClientGameManagerPtr()->getLoadingGame()->onGameEvent(ge);
//			GetClientCSMgr().onGameEvent(ge);
//#ifdef DEDICATED_SERVER
//			SSDataMgr::GetInstance().onGameEvent(ge);
//#endif
//			//ClientDiagnoseMgr::GetInstance().onGameEvent(ge);
//			//RoomSyncResMgr::getSingleton().onGameEvent(ge);
//			//AchievementManager::GetInstance().onGameEvent(ge);
//#if MINI_NEW_UI
//			GetMiniUIGameEventMgr().OnGameEvent(GetGameEventQue().getEventName(ge));
//#endif
//#ifdef DEDICATED_SERVER
//			StandaloneServer::OnServerGameEvent(ge);
//#endif
//#ifdef _WIN32
//			//MicroUpdateMgr::GetInstance().onGameEvent(ge);
//#endif
//		}
//	};

	void ClientApp::HandleEvents()
	{
		OPTICK_EVENT();
	   GetGameEventQue().handleEvents();
	}

	unsigned int ClientApp::GetFrameCount()
	{
		return GetFrameTimeManager().GetFrameCount();
	}

	void ClientApp::SomeInitAfterCsvLoad()
	{
		m_ClientLogin->SomeInitAfterCsvLoad();
		
#ifdef IWORLD_UNIVERSE_BUILD
		GetModuleResManager().Init();
#endif
	}

	ClientLogin* ClientApp::GetClientLogin()
	{
		return m_ClientLogin;
	}

	void ClientApp::CreateClientLogin()
	{
		if (m_ClientLogin == nullptr)
		{
			m_ClientLogin = ENG_NEW(ClientLogin)();
		}
		//初始化设备语言和国家码等，要在热更之前初始化，不然热更阶段的上报信息缺失 code_by:huangfubin 2022.11.19
		//相应的csv配置表需要放置到first_res.pkg中
		m_ClientLogin->InitOsEnvInfo();
		m_ClientLogin->InitSubproject();
		GetClientInfo()->initLanguage();

		// 上报的服务器地址需要预先初始化
		Mini::GetHttpReportMgr().initUrl();
	}

	bool ClientApp::OnCloseConfirm()
	{

#if OGRE_PLATFORM == OGRE_PLATFORM_WIN32
		//热更新时 Lua虚拟机未准备好
		if (!ScriptVM::IsVaild())
			return false;
		ScriptVM::game()->callFunction("SetOutGameConfirm", "i", 2);
#ifdef OGRE_USE_CEF3_LIB
		GetICef3Manager()->ShowCefBrowser(false);
#endif
		return true;
#else
		return false;
#endif
	}

	void ClientApp::OnPause()
	{
		GameApp::OnPause();
#ifdef IWORLD_UNIVERSE_BUILD
		GetModuleResManager().OnPause();
#endif

		// 热更新状态时，跳过 Lua 调用
		if (GetClientInfo()->getAppState() <= APPSTATE_HOTFIXING)
			return;

		if (!ScriptVM::game()) return ;
#ifndef DEDICATED_SERVER
		ScriptVM::game()->callFunction("GamePause", "");
#endif
		ScriptVM::game()->SetEnableTmpCallFunction(false);
		//if (PatchManager::getInstance() && PatchManager::getInstance()->isPatching()) //patch时机比较早，有些对象还没来得及初始化
		//{
		//	return true;
		//}

		//303 海外版本不在这里上报统计数据，在后台发统计可能会死锁导致ANR
#if !defined(UNIVERSE_DEV_BUILD)
		STATISTICS_INTERFACE_EXEC(send(false, false), 0);
#endif 	

		//发送YouMe暂停事件
		if (GetSandBoxManagerPtr()) {
			GetSandBoxManagerPtr()->DoEvent(SandBoxMgrEventID::EVENT_GAMEPLAY_MODULE_YOUME_PAUSE, 0, 0, nullptr, 0);
		}
		//给玩家发一个消息,以用来中断技能释放
		MNSandbox::GetGlobalEvent().Emit<>("playerControlPasuse");
#ifdef MODULE_FUNCTION_ENABLE_ACESDK
		AceSdkMgr::OnPause();
#endif

		//if (m_EngineRoot)
		//	m_EngineRoot->onPause();
		m_AppPaused = true;

#if OGRE_PLATFORM == OGRE_PLATFORM_APPLE
		Mini::GetHttpReportMgr().onPause();
#endif
		//GameRuntime::getSingleton().onPause();

		ScriptVM::game()->SetEnableTmpCallFunction(true);
		ScriptVM::game()->SetEnableCall(false);

		if (GetGameNetManagerPtr())
		{
			GetGameNetManagerPtr()->onPause();
		}
		GetGameNetManagerPtr()->onPause();

		/*return true;*/
	}

	void ClientApp::OnResume()
	{
		GameApp::OnResume();
		
#ifdef IWORLD_UNIVERSE_BUILD
		GetModuleResManager().OnResume();
#endif

		// 热更新状态时，跳过 Lua 调用
		if (GetClientInfo()->getAppState() <= APPSTATE_HOTFIXING)
			return;

		if (ScriptVM::game()) {
			ScriptVM::game()->SetEnableCall(true);
			ScriptVM::game()->SetEnableTmpCallFunction(false);
		}
		//if (PatchManager::getInstance() && PatchManager::getInstance()->isPatching()) //patch时机比较早，有些对象还没来得及初始化
		//{
		//	return false;
		//}

		STATISTICS_INTERFACE_EXEC(send(false, false), 0);

		//if (m_EngineRoot)
		//	m_EngineRoot->onResume();
		m_AppPaused = false;

		GetGameRuntime().onResume();
		if (ScriptVM::game()) ScriptVM::game()->SetEnableTmpCallFunction(true);

		Mini::GetHttpReportMgr().onResume();


#ifndef DEDICATED_SERVER
		GetLuaInterfaceProxy().callLuaString("GameResume();");
#endif

		//发送YouMe恢复事件
		if (GetSandBoxManagerPtr()) {
			GetSandBoxManagerPtr()->DoEvent(SandBoxMgrEventID::EVENT_GAMEPLAY_MODULE_YOUME_RESUME, 0, 0, nullptr, 0);
		}

#ifdef MODULE_FUNCTION_ENABLE_ACESDK
		AceSdkMgr::OnResume();
#endif
		if (GetGameNetManagerPtr())
		{
			GetGameNetManagerPtr()->onResume();
		}
		GetGameNetManagerPtr()->onResume();
	}

	void ClientApp::OnBackPressed()
	{
		if(g_pPlayerCtrl && g_pPlayerCtrl->isDead())
			return;
		GameEventQue::GetInstance().postSimpleEvent("GIE_APPBACK_PRESSED");
	}

	void ClientApp::OnTick(float dt)
	{
		OPTICK_CATEGORY("ClientApp::OnTick", Optick::Category::GameLogic);

		if (GetClientInfo()->getAppState() <= APPSTATE_HOTFIXING) {
			return;
		}

		float deltaTime = dt;
		if (deltaTime > GAME_TICK_TIME)
		{
			deltaTime = GAME_TICK_TIME;
		}
	
		GetChannelConfig().checkAppParams();

//#if !defined(DEDICATED_SERVER) && MODULE_FUNCTION_ENABLE_MINISANDBOX_VIDEODECODER_OPEN_DECODER
//		VideoAudioMgr::getVideoAudioMgrHandler()->updata(0);
//#endif
		
		ScriptVM* scriptVM = ScriptVM::game();

		float record_speed = GAMERECORD_INTERFACE_EXEC(getSpeed(), 1.0f);
		float  tickdistance = deltaTime * record_speed;
		m_GameTickAccum += tickdistance;
		m_NetTickAccum += deltaTime;

		float partialTick = fmod(m_GameTickAccum * 1000, GAME_TICK_MSEC) / GAME_TICK_MSEC;
		bool bEmitTick = false;
		int highTickCnt = 0;  // 需要执行高帧率tick 的次数
		float curtime  = GetTimeSinceStartup();

		SAMPLE_PROFILER_BEGIN(ClientApp_OnTick_1);

#ifdef SANDBOX_HIGH_TICK
		highTickCnt = MNSandbox::Core::CalcTickCnt(dt * record_speed);
#endif //SANDBOX_HIGH_TICK

		if(m_GameTickAccum > GAME_TICK_TIME)//m_GameTickAccum > GAME_TICK_TIME
		{
			bEmitTick = true;
			m_GameTickAccum -= GAME_TICK_TIME;
#ifndef SANDBOX_HIGH_TICK
			highTickCnt = 1;
#endif //SANDBOX_HIGH_TICK
		}

		if (m_NetTickAccum > NET_TICK_TIME)
		{
			m_NetTickAccum -= NET_TICK_TIME;
			if (MNSandbox::Config::GetSingleton().IsSandboxMode())
			{
				if (GetGameNetManagerPtr())
					GetGameNetManagerPtr()->tick();
			}
		}

		if (bEmitTick) // 旧的帧率
		{
			SANDBOXPROFILING_LOG(tickcost, "GameTick :");
#ifdef IWORLD_UNIVERSE_BUILD
			GetModuleResManager().OnTick(dt);
#endif
			SANDBOXPROFILING_STEP(tickcost, "-1-");

			Mini::HttpReportMgr::GetInstancePtr()->update(dt);
			SANDBOXPROFILING_STEP(tickcost, "-2-");
			if (!GAMERECORD_INTERFACE_EXEC(isRecordPlaying(), false)
				|| !GAMERECORD_INTERFACE_EXEC(isRecordStarted(), false)
				|| !GAMERECORD_INTERFACE_EXEC(isPause(), false))
			{
				if (GetClientGameManagerPtr()->getCurGame())
					GetClientGameManagerPtr()->getCurGame()->prepareTick();

				SANDBOXPROFILING_STEP(tickcost, "-3-");

				if (!MNSandbox::Config::GetSingleton().IsSandboxMode() && GetGameNetManagerPtr())
					GetGameNetManagerPtr()->tick();

				SANDBOXPROFILING_STEP(tickcost, "-4-");
				if (highTickCnt > 0)
					MNSandbox::Core::Tick(highTickCnt);

				SANDBOXPROFILING_STEP(tickcost, "-5-");
				CameraManager::GetInstance().tick();

				SANDBOXPROFILING_STEP(tickcost, "-6-");
				GetLuaWebSocketMgr().tick();
				HandleEvents();

				SANDBOXPROFILING_STEP(tickcost, "-7-");
				//MINIW::UIThreadTask::checkQueue();
				ClientGameManager* pCltGameMgr = GetClientGameManagerPtr();
				if (pCltGameMgr)
				{
					
					GetAccountFBData().tick(); //不能屏蔽，这里会更新上传数据

					if (pCltGameMgr->getLoadingGame())
					{
						scriptVM->callFunction("UpdateSystemLoading", "d", deltaTime);
					}
				}

				SANDBOXPROFILING_STEP(tickcost, "-8-");
				if (GetCustomModelWaitSyncListMgrPtr())
					GetCustomModelWaitSyncListMgrPtr()->updateTick();

				SANDBOXPROFILING_STEP(tickcost, "-9-");
				if (GetClientGameManagerPtr()->getCurGame())
					GetClientGameManagerPtr()->getCurGame()->tick();
				MNSandbox::Core::LegacyTick();

				//租赁服检查12号中断
#ifdef DEDICATED_SERVER
				OnServerFrameTick();
#endif
				SANDBOXPROFILING_STEP(tickcost, "-10-");

				{
					OPTICK_EVENT("script_lua_tick");
					// lua container tick ,  用来调度协程 -- add by null start {{{			
					int seconds;
					int microseconds;
					CommonUtil::GetInstancePtr()->gettimeofday(seconds, microseconds);
					if (scriptVM)
					{
#if 1
						lua_State* pLua = scriptVM->getLuaState();
						lua_getglobal(pLua, "__tick__");
						lua_pushnumber(pLua, (int)seconds);
						lua_pushnumber(pLua, (int)microseconds / 1000);
						lua_call(pLua, 2, 0);
#else
						scriptVM->callFunction("__tick__", "ii", seconds, microseconds / 1000);
#endif
					}
					ScriptSupportMgr::GetInstance().Update((double)seconds * 1000 + (double)(microseconds / 1000));
					// lua container tick ,  用来调度协程 -- add by null end }}}

					SANDBOXPROFILING_STEP(tickcost, "-12-")
				}
			}
			else
			{

				if (scriptVM)
				{
					ClientGameManager* pCltGameMgr = ClientGameManager::getInstance();
					if (pCltGameMgr && pCltGameMgr->getLoadingGame()) {
						scriptVM->callFunction("UpdateSystemLoading", "d", deltaTime);
					}					
				}
				if (!MNSandbox::Config::GetSingleton().IsSandboxMode() && GetGameNetManagerPtr())
					GetGameNetManagerPtr()->tick();

				if (highTickCnt > 0)
					MNSandbox::Core::Tick(highTickCnt);

				if (GAMERECORD_INTERFACE_EXEC(isRecordVideo(), false))
				{
					if (GetClientGameManagerPtr()->getCurGame())
						GetClientGameManagerPtr()->getCurGame()->tick();
				}
				else if (g_WorldMgr)
				{
					g_WorldMgr->tick();
				}
				MNSandbox::Core::LegacyTick();
				HandleEvents();
			}
					
			if (Rainbow::SoundSystem::GetInstancePtr())
				Rainbow::SoundSystem::GetInstance().tick();

			SANDBOXPROFILING_STEP(tickcost, "-13-");
			if (GetSandBoxManagerPtr())
				GetSandBoxManagerPtr()->tick();

			SANDBOXPROFILING_STEP(tickcost, "-14-");
		}
		else if (highTickCnt > 0) // 高帧率
		{
			MNSandbox::Core::Tick(highTickCnt);
		}
		SAMPLE_PROFILER_END(ClientApp_OnTick_1);

		SANDBOXPROFILING_LOG(updatecost, "GameUpdate :");

		// todo opt; 保持相机移动时，手持item的平滑移动
		GetClientInfo()->setPartialTick(partialTick);


		float deltaSeconds = dt;

#ifndef DEDICATED_SERVER
		SAMPLE_PROFILER_BEGIN(ClientApp_OnTick_2);
		if (GetClientGameManagerPtr()->getCurGame())
		{
			GetClientGameManagerPtr()->getCurGame()->update(deltaSeconds * record_speed);
			if (g_pPlayerCtrl != NULL && GetClientGameManagerPtr()->getCurGame()->isInGame())
			{
				CameraManager::GetInstance().update(deltaSeconds);
			}
		}
		SAMPLE_PROFILER_END(ClientApp_OnTick_2);

		SAMPLE_PROFILER_BEGIN(ClientApp_OnTick_3);

		MNSandbox::Core::Update(deltaSeconds);

		{
			GET_SUB_SYSTEM(UIActorBodyMgr)->update(deltaSeconds);
		}
		SAMPLE_PROFILER_END(ClientApp_OnTick_3);
		//HomeChest::getInstance()->update(deltaSeconds);

		PlatformSdkManager::GetInstance().update(deltaSeconds);

		SAMPLE_PROFILER_BEGIN(ClientApp_OnTick_4);

		if (SoundSystem::GetInstancePtr())
			SoundSystem::GetInstance().update();

		if ((GetFrameCount() % 90) == 0)
		{
			//if (Rainbow::GetUpDownloadManager())
			{
				int network = GetClientInfo()->getNetworkState();
				GetUpDownloadManager().setNetState(network);
				//GetModuleResManager().SetNetState(network);
			}
			GenCustomModelManager::GetInstance().gcCustomModel(480000);
		}
#ifdef IWORLD_UNIVERSE_BUILD
		if ((GetFrameCount() % 20) == 0)
		{
			int network = GetClientInfo()->getNetworkState();
			GetModuleResManager().SetNetState(network);
		}
#endif

#else
		MNSandbox::Core::Update(deltaSeconds);
#endif

		if ((GetFrameCount() % 120) == 0)
		{
			//防沉迷，版号送审用
			if (!GetClientInfo()->useTpRealNameAuth() && !GetClientInfo()->getAudit())
			{
				if (GetClientGameManagerPtr()->getCurGame())
					scriptVM->callString("if UpdateAntiAddiction~=nil then UpdateAntiAddiction(); end");
			}
		}

		DebugWSClientMgr::GetInstancePtr()->tick();

		SandboxRuntime::getSingleton().FrameUpdate(deltaSeconds, bEmitTick);

		GetClientInfo()->doFrame();
		SAMPLE_PROFILER_END(ClientApp_OnTick_4);
#ifdef IS_POCOSDK_ENABLE
		if (GetDebugMgr().UsePocoTest())
		{
			PocoTest::ScreenShotMgr::GetInstancePtr()->update(dt);
		}
#endif
	}

	bool ClientApp::InitPhysXMgr()
	{
		ScriptVM* scriptVM = ScriptVM::game();
		lua_State* luaState = scriptVM->getLuaState();
		RegisterLLMLuaFunctions(luaState);
		return PhysXManager::GetInstance().Init();
	}

	bool ClientApp::InitTriggerScriptMgr()
	{
#if ENABLE_DEV_VM
		TriggerScriptMgr::GetInstance().Initialize(m_DeveloperScriptVM);
#else
		TriggerScriptMgr::GetInstance().Initialize(ScriptVM::game());
#endif

		return true;
	}


	const char* GetGameStr(int id)
	{
		return StringDefCsv::getInstance()->get(id);
	}

	void ClientApp::SetUserTypePointer()
	{
		ScriptVM* scriptVM = ScriptVM::game();
		lua_State* luaState = scriptVM->getLuaState();
		//STATISTICS_INTERFACE_EXEC(toLuaOpen(), 0);//ClientLogin::InitGameLuaEnv已经设置

#ifdef MODULE_FUNCTION_ENABLE_GAMERECORD
		GetRecordPkgManager();
#endif

#ifdef MODULE_FUNCTION_ENABLE_SNAPSHOT
		GetSnapshotMgrPtr();
#endif
		GetGameInfo();
		GetHomeCsvManager();
		GetStatisticsManager();
		GetDefManager().setUserTypePointer();
		scriptVM->setUserTypePointer("ClientMgr", "ClientManager", GetClientManagerPtr());
		//scriptVM->setUserTypePointer("CSMgr", "ClientCSMgr", GetClientCSMgrPtr());

		AssertMsg(GetOWorldListPtr()->m_CSOWorld, "m_CSOWorld can't be Null!");
		scriptVM->setUserTypePointer("CSOWorld", "ClientCSOWorld", GetOWorldListPtr()->m_CSOWorld);
		scriptVM->setUserTypePointer("WorldBackupMgr", "WorldBackupMgr", GetWorldBackupMgrPtr());
	
		scriptVM->setUserTypePointer("WorldArchiveMgr", "WorldArchiveMgr", GetWorldArchiveMgrPtr());
		scriptVM->setUserTypePointer("RoomManager", "RoomManager", GetRoomManagerPtr());
		scriptVM->setUserTypePointer("ArchiveMgr", "ArchiveManager", GetArchiveManagerPtr());
		scriptVM->setUserTypePointer("OnlineService", "OnlineService", GetOnlineServicePtr());
		scriptVM->setUserTypePointer("EditorLang", "EditorLang", GetEditorLangPtr());

		scriptVM->setUserTypePointer("Advertisement", "Advertisement", Advertisement::getInstance());
#ifdef IWORLD_UNIVERSE_BUILD
		scriptVM->setUserTypePointer("MiniMutualParams", "MiniMutualParams", MINIW::MiniMutualParams::getInstance());
#endif
		scriptVM->setUserTypePointer("SdkManager", "PlatformSdkManager", PlatformSdkManager::GetInstancePtr());

#ifdef MODULE_FUNCTION_ENABLE_AR
		scriptVM->setUserTypePointer("ARInterface", "ARInterface", ARInterface::getInstance());
#endif

#ifdef MODULE_FUNCTION_ENABLE_SNAPSHOT
		scriptVM->setUserTypePointer("Snapshot", "SnapshotMgr", SnapshotMgr::GetInstancePtr());
		scriptVM->setUserTypePointer("SnapshotForShare", "SnapshotForShare", SnapshotForShare::GetInstancePtr());
		scriptVM->setUserTypePointer("SnapshotForGame", "SnapshotForGame", SnapshotForGame::GetInstancePtr());
#endif
		scriptVM->setUserTypePointer("BuddyManager", "ClientBuddyMgr", ClientBuddyMgr::GetInstancePtr());
		//scriptVM->setUserTypePointer("UIActorBodyManager", "UIActorBodyMgr", &GetUIActorBodyMgr());
		scriptVM->setUserTypePointer("AccountManager", "ClientAccountMgr", GetClientAccountMgrPtr());
		scriptVM->setUserTypePointer("AchievementMgr", "AchievementManager", AchievementManager::GetInstancePtr());

		//这个就等于
		//scriptVM->setUserTypePointer("HttpDownloader", "HttpDownloadMgr", HttpDownloadMgr::GetInstancePtr());
		scriptVM->setUserTypePointer("ReportMgr", "HttpReportMgr", Mini::HttpReportMgr::GetInstancePtr());
		//scriptVM->setUserTypePointer("HttpFileUpDownMgr", "HttpFileUpDownMgr", GetHttpFileUpDownMgrPtr());
		scriptVM->setUserTypePointer("DebugMgr", "DebugDataMgr", DebugDataMgr::GetInstancePtr());
		//scriptVM->setUserTypePointer("PermitsMgr", "PermitsManager", PermitsManager::GetInstancePtr());
		scriptVM->setUserTypePointer("ModMgr", "ModManager", ModManager::GetInstancePtr());
		scriptVM->setUserTypePointer("ModEditorMgr", "ModEditorManager", ModEditorManager::GetInstancePtr());
		scriptVM->setUserTypePointer("ModFileMgr", "ModFileManager", ModFileManager::GetInstancePtr());

#ifdef MODULE_FUNCTION_ENABLE_SPRINGfESTIVAL
#if _SFACTIVITY_STATE == 1
		scriptVM->setUserTypePointer("SFActivityMgr", "SpringFestivalActivityMgr", SpringFestivalActivityMgr::GetInstancePtr());
#endif
#endif

		//UI编辑器:TODO
		scriptVM->setUserTypePointer("UIProjectLibMgr", "UIProjectLibManager", UIProjectLibManager::getSingleton());
		scriptVM->setUserTypePointer("UIEditorMgr", "UIEditorManager", UIEditorManager::getSingleton());
		scriptVM->setUserTypePointer("DevUIFrameMgr", "UIFrameManager", UIFrameManager::getSingleton());
		scriptVM->setUserTypePointer("g_DevEncrypt", "DevEncrypt", DevEncrypt::getSingleton());
		scriptVM->setUserTypePointer("CustomPicMgr", "CustomPicMgr", CustomPicMgr::getSingleton());

		//蓝图
		//scriptVM->setUserTypePointer("BluePrintMgr", "BluePrintMgr", GetBluePrintMgrPtr());//修改csv的加载到loading过程中以后，也需要重新更新下userdata
		scriptVM->setUserTypePointer("RoomSyncResMgr", "RoomSyncResMgr", RoomSyncResMgr::getSingletonPtr());
		scriptVM->setUserTypePointer("g_MidiFileInfo", "MidiFileInfo", MidiFileInfo::getSingleton());

		//星站信息
		scriptVM->setUserTypePointer("StarStationTransferMgr", "StarStationTransferMgr", StarStationTransferMgr::GetInstancePtr());

		//地图编辑
		scriptVM->setUserTypePointer("MapEditManager", "MapEditManager", MapEditManager::GetInstancePtr());
		//AI方法管理器
		//scriptVM->setUserTypePointer("AIFunctionMgr", "AIFunctionMgr", AIFunctionMgr::GetInstancePtr());

#ifndef DEDICATED_SERVER
#if OGRE_PLATFORM == OGRE_PLATFORM_WIN32
		scriptVM->setUserTypePointer("MicroUpdateMgr", "MicroUpdateMgr", MicroUpdateMgr::GetInstancePtr());
#endif

		scriptVM->setUserTypePointer("GameUI", "GameUI", GetGameUIPtr());
#else
		scriptVM->setUserTypePointer("zmqMgr_", "ZmqMgr", g_zmqMgr);
		scriptVM->setUserTypePointer("GameUI", "GameUI", NULL);
		scriptVM->setUserTypePointer("MicroUpdateMgr", "MicroUpdateMgr", NULL);
		scriptVM->setUserTypePointer("TeleportRegisterManager", "TeleportRegisterManager", TeleportRegisterManager::getInstance());
#endif

		scriptVM->setUserTypePointer("ClientGameMgr", "ClientGameManager", GetClientGameManagerPtr());

		scriptVM->setUserTypePointer("WorldStringManager", "WorldStringManager", WorldStringManager::GetInstancePtr());

		scriptVM->setUserTypePointer("CameraMgr", "CameraManager", CameraManager::GetInstancePtr());
		scriptVM->setUserTypePointer("ScriptSupportMgr", "ScriptSupportMgr", ScriptSupportMgr::GetInstancePtr());
		scriptVM->setUserTypePointer("WorldCfg", "IWorldConfig", GetIWorldConfigPtr());
		scriptVM->setUserTypePointer("PhysXMgr", "PhysXManager", PhysXManager::GetInstancePtr());
		scriptVM->setUserTypePointer("GameSettingsMgr", "GameSettings", GameSettings::GetInstancePtr());
		scriptVM->setUserTypePointer("UIStageObj", "UIStageSandboxObject", UIStageSandboxObject::getInstance());

		scriptVM->setUserTypePointer("MinimapRenderer", "MinimapRenderer", MinimapRenderer::GetInstancePtr());
		scriptVM->setUserTypePointer("FileAndDirInfo", "FileAndDirInfo", FileAndDirInfo::getInstance());
		scriptVM->setUserTypePointer("CustommodelParse", "CustommodelParse", CustommodelParse::getInstance());
		scriptVM->setUserTypePointer("GlobalSetParse", "GlobalSetParse", GlobalSetParse::getInstance());
		scriptVM->setUserTypePointer("WXGameLiveMgr", "WXGameLiveManager", WXGameLiveManager::getInstance());
#ifdef IWORLD_UNIVERSE_BUILD
		scriptVM->setUserTypePointer("ModuleResMgr", "ModuleResManager", &GetModuleResManager());
#endif
		scriptVM->setUserTypePointer("GameConfigMgr", "GameConfigMgr", Rainbow::Setting::GameConfigMgr::GetInstance());
		scriptVM->setUserTypePointer("DHKeyMgr", "DHKeyMgr", GetDHKeyMgrPtr()); //DH秘钥管理
	}

	bool ClientApp::LoadScriptTOC(const char* tocfile)
	{
		if (!tocfile) return false;
		LogStringMsg("load script toc file %s", tocfile);
		if(tocfile==nullptr) return false;
		AutoRefPtr<Rainbow::DataStream> fp = GetFileManager().OpenFile(tocfile, true);
		if (!fp)
		{
			ErrorStringMsg("failed to open script toc file %s", tocfile);
			return false;
		}

		char buffer[1024];
		bool b = true;
		while (!fp->Eof())
		{
			fp->ReadLine(buffer, sizeof(buffer));
			std::string inbuf = buffer;

			size_t pos = inbuf.find(".lua");
			if ((pos != inbuf.npos) && ((inbuf.find("##")) > 0))
			{
				if (strchr(inbuf.c_str() + pos + 4, 'p'))
				{
					std::string path = inbuf.substr(0, pos + 4);
					b = ScriptVM::game()->loadPackage(path.c_str());
				}
				else {
					b = ScriptVM::game()->callFile(inbuf.c_str());
				}

				if (!b)
				{
					ErrorStringMsg("load script lua file failed: %s", inbuf.c_str());
					break;
				}
				LogStringMsg("load script lua file ok: %s", inbuf.c_str());
			}
		}
		return b;
	}

	
	 

	ClientApp& GetClientApp()
	{
		return static_cast<ClientApp&>(GetGameApp());
	}

	bool ClientApp::checkDebuggerRunning()
	{
#ifndef IWORLD_DEV_BUILD
		{
			VMP_BEGIN("ClientApp_checkDebuggerRunning");
			LOG_INFO("start");
			if (VMP_IS_DEBUGGER_PRESENT(TRUE))
			{
				STATISTICS_INTERFACE_EXEC(launchGame(3), 0);
#ifdef _WIN32
				LANGID wLangPID = PRIMARYLANGID(::GetUserDefaultLangID());
				if (wLangPID == LANG_CHINESE)
				{
					MessageBox(NULL, _T("检测到调试器运行，请先退出调试器再启动游戏！"), _T("警告"), MB_OK | MB_ICONEXCLAMATION);
				}
				else
				{
					MessageBox(NULL, _T("Detected that the debugger is running, please exit the debugger before starting the game!"), _T("Warning"), MB_OK | MB_ICONEXCLAMATION);
				}
#endif
				VMP_END;
				return true;
			}

			LOG_INFO("end");
			VMP_END;
		}
#endif
		return false;
	}


	bool ClientApp::preOpenMicroMini()
	{
#ifdef IWORLD_TARGET_PC
#ifndef IWORLD_DEV_BUILD
		if (GetMiniWorldPreferences().getHash().empty())
		{
			VMP_BEGIN("preOpenMicroMini");
			int nShow = SW_NORMAL;
			if (WIN_UTIL::IsVistaSystemAbove())
			{
				//vista
				SHELLEXECUTEINFO shExecInfo;
				shExecInfo.cbSize = sizeof(SHELLEXECUTEINFO);
				shExecInfo.fMask = NULL;
				shExecInfo.hwnd = 0;
				shExecInfo.lpVerb = __TEXT("open");
#ifdef IWORLD_UNIVERSE_BUILD
				shExecInfo.lpFile = __TEXT("MicroMiniNew.exe");
#else
				shExecInfo.lpFile = __TEXT("MicroMini.exe");
#endif
				shExecInfo.lpParameters = __TEXT("");
				shExecInfo.lpDirectory = NULL;
				shExecInfo.nShow = nShow;
				shExecInfo.hInstApp = NULL;
				ShellExecuteEx(&shExecInfo);
			}
			else
			{
#ifdef IWORLD_UNIVERSE_BUILD
				ShellExecute(0, __TEXT("open"), __TEXT("MicroMiniNew.exe"), __TEXT(""), NULL, nShow);
#else
				ShellExecute(0, __TEXT("open"), __TEXT("MicroMini.exe"), __TEXT(""), NULL, nShow);
#endif
			}
			VMP_END;
			return true;
		}
#endif
#endif
		return false;
	}

	bool ClientApp::checkIsGMWhiteListMember()
	{
#ifdef _WIN32
		return GameGMMgr::GetInstance()->checkIsWhiteListMember();
#else
		return false;
#endif
	}

	void ClientApp::DevUISetIconByResIdExProxy(GLoader* pLoader, std::string strLongId)
	{
		DEVUISetIconByResIdEx(pLoader, strLongId);
	}

	bool ClientApp::checkDoubleOpenings()
	{
#ifdef _WIN32
		//WarningStringMsg("checkDoubleOpenings mac :%s", GameGMMgr::GetInstance()->getMacAddr().c_str());
		//if (GameGMMgr::GetInstance()->checkIsWhiteListMember())
		//{
		//	//WarningStringMsg("checkDoubleOpenings is GM");
		//	return false;
		//}
#endif

#ifndef IWORLD_DEV_BUILD
#ifdef _WIN32
		{
			VMP_BEGIN("checkDoubleOpenings");
			LOG_INFO("start");
			HANDLE hMutex = CreateMutex(NULL, FALSE, __TEXT("@@miniworld@@"));
			if (hMutex && (GetLastError() == ERROR_ALREADY_EXISTS))
			{
				LANGID wLangPID = PRIMARYLANGID(::GetUserDefaultLangID());
				if (wLangPID == LANG_CHINESE)
				{
					MessageBox(NULL, _T("本版本不能双开"), _T("提醒"), MB_OK);
				}
				else
				{
					MessageBox(NULL, _T("Multi-boxing is not Available!!"), _T("System Notification"), MB_OK);
				}
				CloseHandle(hMutex);

				LOG_INFO("end");
				VMP_END;
				return true;
			}
		}
#endif // _WIN32
#else
		{
			static CMiniDumper dump(true); //win10版本可能CMiniDumper初始化有错。系统版本不好精确判断或者获取版本的函数也难保证兼容性暂时在编译pc正式版时屏蔽掉 
		}
#endif
		return false;
	}

	bool ClientApp::enableNewAppUpdate()
	{
		bool EnableAllChannel = IModuleConf::getBool(false, "ColdUpdate.EnableAllChannel");
		if (!EnableAllChannel)
		{
			bool isChannelEnable = false;
			int curApiId = GetClientInfo()->getApiId();
			const char* EnableApiid = IModuleConf::getString("", "ColdUpdate.EnableApiid");
			auto apiidVec = Rainbow::StringUtil::split(core::string(EnableApiid), "|");
			for (auto iter = apiidVec.begin(); iter != apiidVec.end(); iter++)
			{
				if ((*iter).compare(IntToString(curApiId)) == 0)
				{
					isChannelEnable = true;
					break;
				}
			}
			if (!isChannelEnable)
			{
				return false;
			}
		}
		bool EnableAllLanguage = IModuleConf::getBool(false, "ColdUpdate.EnableAllLanguage");
		if (EnableAllLanguage)
		{
			return true;
		}
		bool isLanguageEnable = false;
		int curLang = GetClientInfo()->getGameData("lang");
		const char* EnableApiid = IModuleConf::getString("", "ColdUpdate.EnableApiid");
		auto apiidVec = Rainbow::StringUtil::split(core::string(EnableApiid), "|");
		for (auto iter = apiidVec.begin(); iter != apiidVec.end(); iter++)
		{
			if ( (*iter).compare(IntToString(curLang)) == 0)
			{
				return true;
			}
		}
		return false;
	}

	bool ClientApp::checkVMPValid()
	{
		// 暂时注释，解决卡顿问题
//		if (!VMP_IS_VALID_IMAGE_CRC())
//		{
//			//VMP_BEGIN("ClientApp_checkVMPValid");
//			LOG_INFO("start");
//			STATISTICS_INTERFACE_EXEC(launchGame(2), 0);
//#ifdef _WIN32
//			LANGID wLangPID = PRIMARYLANGID(::GetUserDefaultLangID());
//			if (wLangPID == LANG_CHINESE)
//			{
//				MessageBox(NULL, _T("游戏文件被损坏，请重新安装！"), _T("警告"), MB_OK | MB_ICONEXCLAMATION);
//			}
//			else
//			{
//				MessageBox(NULL, _T("Game files corrupted, please reinstall the game!"), _T("Warning"), MB_OK | MB_ICONEXCLAMATION);
//			}
//#endif
//			LOG_INFO("end");
//			//VMP_END;
//			return false;
//		}
		return true;
	}

	bool ClientApp::checkUnderVM()
	{
#ifndef ENABLE_VM_RUN
		{
			VMP_BEGIN("ClientApp_checkUnderVM");
			LOG_INFO("start");
			if (VMP_IS_VIRTUAL_MACHINE_PRESENT())
			{
				STATISTICS_INTERFACE_EXEC(launchGame(4), 0);
#ifdef _WIN32
				LANGID wLangPID = PRIMARYLANGID(::GetUserDefaultLangID());
				if (wLangPID == LANG_CHINESE)
				{
					MessageBox(NULL, _T("抱歉，本游戏无法在虚拟机下运行！"), _T("警告"), MB_OK | MB_ICONEXCLAMATION);
				}
				else
				{
					MessageBox(NULL, _T("Sorry, this game cannot run under a Virtual Machine."), _T("Warning"), MB_OK | MB_ICONEXCLAMATION);
				}
				return true;
#endif
			}
			LOG_INFO("end");
			VMP_END;
		}
#endif
		return false;
	}

	void ClientApp::OnParseCmdStr(const char* cmdStr)
	{
		//BootConfig::Init(&cmdStr,1);
		VMP_BEGIN("ClientApp_OnParseCmdStr");
		GetMiniWorldPreferences().initHashParam(cmdStr);
		int apiid = 0;
		core::string m_CacheDir = "";
		
#if PLATFORM_WIN

		bool isministudio = false;
#if BUILD_MINI_EDITOR_APP && !defined SANDBOX_DEV
		isministudio = true;
#endif

		//对外发布的模式
		if( (!IsAssetDevelopMode() && GetGameSetting().m_PublishForTarget) || isministudio)
		{
			wchar_t szBuffer[MAX_PATH] = { 0 };
			core::string userPath;
			if (SHGetSpecialFolderPathW(NULL, szBuffer, CSIDL_APPDATA, FALSE)) {
				 ConvertWindowsPathName(szBuffer, userPath);
			}
			//目录路径
			

			apiid = GetClientInfo()->getApiId();

			if (!isministudio) {
				core::string dataPath = Format("%s\\miniworddata%d\\", userPath.c_str(), apiid);
				//WIN_UTIL::CreateDirectoryRecursive(dataPath.c_str());
				//转化成unicode
				CreateDirectoryRecursive(dataPath);
				m_WritePathDir = dataPath;  // C:/Users/<USER>/AppData/Roaming/miniworddata<apiId>/
			}
			else
			{
				//core::string dataPath = Format("%s\\MiniWorldStudioData\\", userPath.c_str());
				//WIN_UTIL::CreateDirectoryRecursive(dataPath.c_str());	

				//core::string scrDataPath = "中文文件夹";
				core::string scrDataPath = "MiniWorldStudioData\\";
				core::string targetDataPath = ConvertWindowsPath(scrDataPath);
				core::string dataPath = AppendPathName(userPath, targetDataPath);

				CreateDirectoryRecursive(dataPath);
				m_WritePathDir = dataPath;				
			}
		}
		m_CacheDir = m_WritePathDir;
#elif PLATFORM_LINUX
		//TODO

#elif PLATFORM_OHOS
        m_WritePathDir = Rainbow::FileUtils::GetWritableFilesPath();
        m_CacheDir = Rainbow::FileUtils::GetWritableCachePath();
#else

		//在android，ios平台下面的写入目录
		static BootConfig::Parameter<const char*>  s_dataDir("dataDir", "");
		static BootConfig::Parameter<const char*>  s_cacheDir("cacheDir", "");
		m_WritePathDir = s_dataDir;
		m_CacheDir = s_cacheDir;
		if (m_CacheDir.empty())
			m_CacheDir = m_WritePathDir;  // 向下兼容，以前没有 cacheDir 路径。

		apiid = GetClientInfo()->getApiId();
#endif

		//打印apiid的信息
		LogStringMsg("Client init apiId:%d, writePath:%s, cacheDir:%s", apiid, m_WritePathDir.c_str(), m_CacheDir.c_str());
		VMP_END;
	}

	// set fps from PlayerSettings
	void ClientApp::SetLimitFPS(bool isLimitFPS)
	{
		const auto frameRate = GetPlayerSettings().GetTargetFrameRate();
		
		this->SetFPS(frameRate);
	}

	// set fps from lua
	void ClientApp::SetLimitFPSFromLua(bool isLimitFPS/*deprecated 保留为将来使用*/, int frameRate)
	{
		Assert(frameRate > 0 && frameRate <= 1000);
		if(frameRate <= 0 || frameRate > 1000 )
		{
			// if get error value from lua, use standard method to set frameRate
			this->SetLimitFPS(isLimitFPS);
			return;
		}

		this->SetFPS(frameRate);
	}

	void ClientApp::SetFPS(int frameRate)
	{
		Assert(frameRate > 0 && frameRate <= 1000);

		auto logic_framerate = GetPlayerSettings().GetTargetFrameRate();
		if (logic_framerate > frameRate)
		{
			LOG_WARNING("render frame rate[%d] is lower than logic frame rate[%d]", frameRate, logic_framerate);
			logic_framerate = frameRate;
		}

		this->SetFrameTickTime(logic_framerate);
		GetFrameTimeManager().SetTargetFrameRate(frameRate);
	}

	bool ClientApp::InitGameAnalytics()
	{
		char deviceId[128] = {0};
		MINIW::GenerateUniqueDeviceID(deviceId, sizeof(deviceId));
		int env = GetIWorldConfig().getGameData("game_env");
		bool ret =  GameAnalytics::Init(deviceId, env);
		if (!ret) {
			LOG_WARNING("InitGameAnalytics failed");
		} else {
			LOG_WARNING("Init GameAnalytics success");
		}
		return ret;
	}

	std::string ClientApp::getAceInfo()
	{
#ifdef MODULE_FUNCTION_ENABLE_ACESDK
		return AceSdkMgr::GetSdkCoreData();
#endif
		return "";
	}

	bool ClientApp::checkInsidePkgCompress()
	{
#if PLATFORM_ANDROID // PLATFORM_OHOS 不使用压缩 pkg，包括 material_ogles3.pkg
		dynamic_array<GamePackageInfo>& infoList = GetGameSetting().m_PkgLists;
		for (int i = 0; i < infoList.size(); i++) {
			GamePackageInfo& info = infoList[i];
			bool isSuportRenderer = true;
			bool isLuajitMeet = true;
			if (info.renderer != kGfxRendererUnknown && info.renderer != Rainbow::GetGfxDeviceSetting().GetGfxDeviceRenderer())
			{
				isSuportRenderer = false;
			}
#if PLATFORM_ARCH_32
			int luajitVersion = 32;
#else
			int luajitVersion = 64;
#endif

			if (info.luajitVersion != 0 && info.luajitVersion != luajitVersion)
			{
				isLuajitMeet = false;
			}
			if (isSuportRenderer && isLuajitMeet) {
				core::string fullPkgFile = "";
				core::string useageFullFile = ToPkgUseageFullFile(info.pkgFilePath.c_str());
				if (GetGameSetting().m_AndroidPKGMode == 2) // InsideApk pkg 模式
				{
					fullPkgFile = "assets/" + info.pkgFilePath;
					int baseVersion = PackageAsset::GetPkgFileVersion(fullPkgFile);
					int newVersion = PackageAsset::GetPkgFileVersion(useageFullFile);
					if (baseVersion <= newVersion)
					{
						fullPkgFile = useageFullFile;
					}
					WarningStringMsg("pkg name:%s,fullPkgFile:%s %d %d", info.pkgFilePath.c_str(), fullPkgFile.c_str(), baseVersion, newVersion);
				}
				else
				{
					fullPkgFile = useageFullFile;
				}
				
				if (FileCompressHelper::CheckLZMAFileCorrect(fullPkgFile.c_str()))
				{
					return true;
				}
			}
		}
#endif

		return false;
	}
}

